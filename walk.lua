--****************************--
--       客户端行走           --
--****************************--
--【1】gt xxx 自动辅助行走
add_trigger("record1","记录房间信息",function()
local ggps=gps(var["roomzone"],var["roomname"],var["roomdesc"],var["roomnearby"],var["roomexit"],var["roomdesc2"])
if ggps=="" then
	echo("\n"..C.W.."room：#var "..var["roomzone"]..var["roomname"].."|"..var["roomnearby"].."|"..var["roomexit"])
	echo(C.Y.."无法定位!!!")
else
	echo("\n"..C.W.."room：#var "..var["roomzone"]..var["roomname"].."|"..var["roomnearby"].."|"..var["roomexit"].."|"..ggps)
	var["ggps"]=ggps
end
end)
add_alias("gt",function(params)

	var["do_stop"]=0
	function after_goto() 
		exec('look;alias action 记录房间信息')
	end

	local gt=tostring(params[-1])
	gt=string.match(gt,"(.*%S)%s*$")
	echo("\n"..C.W.."gt: "..gt)
	if string.find(gt,"玄铁剑") or string.find(gt,"xuantie jian") then
		rooms[1861].exits["dian shuzhi;crush busy;l qingtai;mo qingtai;crush busy;look zi;look mu;kneel mu;crush busy;zuan dong;crush busy"]=3955
	end

	if string.find(gt,"^%d+$") then --房间号？
	
		g(tonumber(gt),function()
			rooms[1861].exits["dian shuzhi;crush busy;l qingtai;mo qingtai;crush busy;look zi;look mu;kneel mu;crush busy;zuan dong;crush busy"]=nil
			send("alias action 到达目的地点...")
		end)
		
	elseif string.find(gt,"^%S-%s+%S+") then --中间有空格？
		local _zone,_room=string.match(gt,"^(%S-)%s+(%S+)$")
		if change_zoneroom[_zone] then 
			_zone=change_zoneroom[_zone] -- _zone
		end
		
		local _number=0
		for k,v in pairs(rooms) do
			if v["area"]==_zone and v["name"]==_room then
				_number=k
				break
			end
		end
		
		if _number~=0 then --是  扬州城 当铺   这种
			g(_number,function()
				rooms[1861].exits["dian shuzhi;crush busy;l qingtai;mo qingtai;crush busy;look zi;look mu;kneel mu;crush busy;zuan dong;crush busy"]=nil
				send("alias action 到达目的地点...")
			end)

		else --再看看obj 物品吧
			_number=0
				for k,v in pairs(rooms) do
					for m,n in pairs(v["obj"]) do
						if m==gt or n==gt then
							_number=k
							break
						end
					end
				end
			
			if _number~=0 then
				g(_number,function()
					rooms[1861].exits["dian shuzhi;crush busy;l qingtai;mo qingtai;crush busy;look zi;look mu;kneel mu;crush busy;zuan dong;crush busy"]=nil
					send("alias action 到达目的地点...")
				end)
			end
		end		
	elseif string.find(gt,"^%S+$") then --没空格，完整的字
		local _zone,_room=break_zone_room(gt)
		if _zone=="" then --无法分解
				local _number=0
				for k,v in pairs(rooms) do
				if v["area"]==gt or v["name"]==gt then
					_number=k
					break
				else
					for m,n in pairs(v["obj"]) do
						if m==gt then
							_number=k
							break
						end
					end
				end
			end

			if _number~=0 then
				g(_number,function()
				rooms[1861].exits["dian shuzhi;crush busy;l qingtai;mo qingtai;crush busy;look zi;look mu;kneel mu;crush busy;zuan dong;crush busy"]=nil
					send("alias action 到达目的地点...")
				end)
			end	
	else
		if change_zoneroom[_zone] then 
			_zone=change_zoneroom[_zone] -- _zone
		end
		local _number=0
		for k,v in pairs(rooms) do
			if v["area"]==_zone and v["name"]==_room then
				_number=k
				break
			end
		end
		
		if _number~=0 then
				g(_number,function()
				rooms[1861].exits["dian shuzhi;crush busy;l qingtai;mo qingtai;crush busy;look zi;look mu;kneel mu;crush busy;zuan dong;crush busy"]=nil
					send("alias action 到达目的地点...")
				end)
		end	
	end

	else --其他情况？

	end
end)

--【2】sz xxx 为了兼容mc sz
add_alias("sz",function(params) 
	var["do_stop"]=0
	function after_goto() end

	local gt=tostring(params[-1])
	gt=string.match(gt,"(.*%S)%s*$")
	echo("\n"..C.W.."gt: "..gt)	
	if string.find(gt,"玄铁剑") or string.find("xuantie jian") then
		rooms[1861].exits["dian shuzhi;crush busy;l qingtai;mo qingtai;crush busy;look zi;look mu;kneel mu;crush busy;zuan dong;crush busy"]=3955
	end
	if string.find(gt,"^%d+$") then --房间号？
	
		g(tonumber(gt),function()
			send("alias action 到达目的地点...")
		end)
		
	elseif string.find(gt,"^%S-%s+%S+") then --中间有空格？
		local _zone,_room=string.match(gt,"^(%S-)%s+(%S+)$")
		if change_zoneroom[_zone] then 
			_zone=change_zoneroom[_zone] -- _zone
		end
		
		local _number=0
		for k,v in pairs(rooms) do
			if v["area"]==_zone and v["name"]==_room then
				_number=k
				break
			end
		end
		
		if _number~=0 then --是  扬州城 当铺   这种
			g(_number,function()
				send("alias action 到达目的地点...")
			end)

		else --再看看obj 物品吧
			_number=0
				for k,v in pairs(rooms) do
					for m,n in pairs(v["obj"]) do
						if m==gt or n==gt then
							_number=k
							break
						end
					end
				end
			
			if _number~=0 then
				g(_number,function()
					send("alias action 到达目的地点...")
				end)
			end
		end		
	elseif string.find(gt,"^%S+$") then --没空格，完整的字
		local _zone,_room=break_zone_room(gt)
		if _zone=="" then --无法分解
				local _number=0
				for k,v in pairs(rooms) do
				if v["area"]==gt or v["name"]==gt then
					_number=k
					break
				else
					for m,n in pairs(v["obj"]) do
						if m==gt then
							_number=k
							break
						end
					end
				end
			end

			if _number~=0 then
				g(_number,function()
					send("alias action 到达目的地点...")
				end)
			end	
	else
		if change_zoneroom[_zone] then 
			_zone=change_zoneroom[_zone] -- _zone
		end
		local _number=0
		for k,v in pairs(rooms) do
			if v["area"]==_zone and v["name"]==_room then
				_number=k
				break
			end
		end
		
		if _number~=0 then
				g(_number,function()
					send("alias action 到达目的地点...")
				end)
		end	
	end

	else --其他情况？

	end
end)

--【3】goto 已经定位且为房间号
add_alias("goto",function(params)
	if params[1]~="" and type(tonumber(params[1]))=="number" then
		local fromid,toid=tonumber(var["location"]),tonumber(params[1])
		if not fromid or fromid==0 then
						if var["debug"] and var["debug"]>2 then echo("\n"..C.x.."<Debug>:goto行走无当前房间号。") end
		else
			local do_next=pathfrom(fromid,toid,lua_flags)
			    tatalpath = {}
				totalpath=list_to_table(string.gsub(do_next,";","|")) --路径表
				breakpath(do_next)
			    dowalk(var["path_before"])
		end
	end
end)
function after_goto() end

--****************************--
--       路径分割执行         --
--****************************--

--【1】breakpath -- 分解路径
function breakpath(path) 
	var["path_cross"],var["path_before"],var["path_after"],var["path_count"]="","","",1
	var["walk_step"]=var["walk_step"] or 20
	local path_before,path_after,path_count="","",0
	if type(path)=="nil" or path=="" then --分解空路径？
		path_before_raw,path_before,path_after="","",""
	else
		path=string.gsub(path,"qiankun%-danuoyi","qiankundanuoyi") --
		path=string.gsub(path,"douzhuan%-xingyi","douzhuanxingyi") --先去除特殊符号"-"
		if var["shen"] and var["shen"]<0 then path=string.gsub(path,"crush ding mian;","") end
		if var["master"] and var["master"]=="无名老僧" then path=string.gsub(path,"crush murong bo;","") end
		if var["party"] and var["party"]=="明教" then path=string.gsub(path,"crush yin wushou;","")  path=string.gsub(path,"crush fan yao;","")  path=string.gsub(path,"crush yang xiao;","") end

		-- path 中含有cross or crush (字母cr) 或 path 大于指定步数目var["walk_step"]
		for match in string.gmatch(path..";","(.-);") do
			path_count=path_count+1
			path_before_raw=path_before or ""
			path_before=path_before..";"..match
			if string.find(match,"^cr") then
				var["path_cross"]=match
				path_after=string.match(";"..path,"^"..path_before.."(.+)") or ""
				path_before=path_before_raw
				break
			elseif path_count==var["walk_step"] then
				path_after=string.match(";"..path,"^"..path_before.."(.+)") or ""
				break
			end
		end
		path_after=string.match(path_after,"^;(.+)") or path_after --除去以";"开始的符号
		path_before=string.match(path_before,"^;(.+)") or path_before--除去以";"开始的符号
		path_before=string.gsub(path_before,"qiankundanuoyi","qiankun%-danuoyi") --恢复特殊符号"-"
		path_before=string.gsub(path_before,"douzhuanxingyi","douzhuan%-xingyi")

		
		var["path_before"],var["path_after"],var["path_count"]=path_before,path_after,path_count
	end
end

--【2】dowalk -- 行走路径
function dowalk(path)
	if type(path)=="nil" or path=="" then
		if var["path_cross"]=="" then
			open_trigger("walk_2")
			send("alias action 行走完成...")
		else
							if var["debug"] and var["debug"]>2 then echo("\n"..C.x.."<Debug>:路径【"..var["path_cross"].."】") end	
			exec(var["path_cross"])
		end
	else
		if var["path_cross"]=="" then
			if var["path_after"]=="" then
				open_trigger("walk_2")
					if var["port"] and var["port"]==2374 then
					path=path..";alias action 行走完成..."
					else
					path=path..";yun jingli;alias action 行走完成..."
					end
							if var["debug"] and var["debug"]>2 then echo("\n"..C.x.."<Debug>:路径【"..path.."】") end
				wait1(var["path_count"]+3,function()
					check_busy(function()
						local ado=var["ado"] or 0
						--[[if ado==1 then
							if var["debug"] and var["debug"]>0 then echo("ado "..string.gsub(path,";","|")) end
							send("ado "..string.gsub(path,";","|"))
							alarm("do_walk",2,function()
								exec("gps")
							end)		
						else]]
						if ado==2 then
							send("alias gogogo "..path)
							exec("gogogo")
							alarm("do_walk",2,function()
								exec("gps")
							end)
						else
							exec(path)
						end

					end)
				end)
			else
				open_trigger("walk_1")
				path=path..";yun jingli;alias action 休息、休息一下..."
							if var["debug"] and var["debug"]>2 then echo("\n"..C.x.."<Debug>:路径【"..path.."】") end
				wait1(var["path_count"]+3,function()
					check_busy(function()
						local ado=var["ado"] or 0
						--[[if ado==1 then
							if var["debug"] and var["debug"]>0 then echo("ado "..string.gsub(path,";","|")) end
							send("ado "..string.gsub(path,";","|"))
						--	send("alias action 休息、休息一下...")
												alarm("do_walk",2,function()
							exec("gps")
						end)
						else]]
						if ado==2 then
							send("alias gogogo "..path)
							exec("gogogo")
													alarm("do_walk",2,function()
							exec("gps")
						end)
						else
							exec(path)
						end

					end)
				end)							
			end
		else
			open_trigger("walk_3")
			path=path..";yun jingli;alias action 暂停、暂停一下..."
							if var["debug"] and var["debug"]>2 then echo("\n"..C.x.."<Debug>:路径【"..path.."】") end
				wait1(var["path_count"]+3,function()
					check_busy(function()
						local ado=var["ado"] or 0
						--[[if ado==1 then
							if var["debug"] and var["debug"]>0 then echo("ado "..string.gsub(path,";","|")) end
							send("ado "..string.gsub(path,";","|"))
						--	send("alias action 暂停、暂停一下...")
												alarm("do_walk",2,function()
							exec("gps")
						end)
						else]]
						if ado==2 then
							send("alias gogogo "..path)
							exec("gogogo")
													alarm("do_walk",2,function()
							exec("gps")
						end)
						else
							exec(path)
						end

					end)
				end)			
		end

	end
end
--type(tonumber(port))
--【3】keepwalk -- 继续行走路径
function keepwalk()
	local do_stop=var["do_stop"] or 0
	if do_stop==0 then
		local do_walk_one = var.do_walk_one or 0  --单独做的一步一走的
		if do_walk_one == 0 then
		   breakpath(var["path_after"]) --继续分解剩余路径
		   dowalk(var["path_before"]) --行走路径
		else
			
			check_busy2(function()					
			    wait(var["walk_wait"],function()
					exec("alias action 可以继续走了么...")
				end)
            end)	
        end		
	end
end

--****************************--
--			触发部分          --
--****************************--
--【1】 中途休息
add_trigger("walk_1","^\\s*你把\\s+\"action\"\\s+设定为\\s+\"休息、休息一下",function(params)
	close_trigger("walk_1")
	del_timer("do_walk")--判断走路的定时器
	local do_stop=var["do_stop"] or 0
	if do_stop==0 then
		if var["wrong_way"] and var["wrong_way"]~=0 then echo("\n"..C.x.."<Debug>:路径出错【"..var["wrong_way"].."步】") end	
		if var["wrong_way"] and var["wrong_way"]==0 then
			keepwalk()
		else
			exec("gps")
		end
	end
end)
--【2】 行走结束
add_trigger("walk_2","^\\s*你把\\s+\"action\"\\s+设定为\\s+\"行走完成",function(params)
	close_trigger("walk_2")
	del_timer("do_walk")--判断走路的定时器
		local n=collectgarbage("count")
		collectgarbage("collect")
		echo("\n"..C.x.."LUA内存清理："..C.c..n..C.x.." --> "..C.y..collectgarbage("count"))
	local do_stop=var["do_stop"] or 0
	if do_stop==0 then
							if var["wrong_way"] and var["wrong_way"]~=0 then echo("\n"..C.x.."<Debug>:路径出错【"..var["wrong_way"].."步】") end	
		if var["wrong_way"] and var["wrong_way"]==0 then
			after_goto()
		else
			exec("gps")
		end
	end
end)

--【3】 中途含有特殊路径"cr" (cross or crush)
add_trigger("walk_3","^\\s*你把\\s+\"action\"\\s+设定为\\s+\"暂停、暂停一下",function(params)
	close_trigger("walk_3")
	del_timer("do_walk")--判断走路的定时器
	local do_stop=var["do_stop"] or 0
	--local check=npc_in_maze_found()
	local	check=nil	
	if check==nil then
		if do_stop==0 then
			if var["wrong_way"] and var["wrong_way"]~=0 then echo("\n"..C.x.."<Debug>:路径出错【"..var["wrong_way"].."步】") end	
			if var["wrong_way"] and var["wrong_way"]==0 then
				if 1==0 and var["path_cross"]=="cross_for_job" then --暂时关闭
					keepwalk()
				else
					exec(var["path_cross"])
				end
			else	
				exec("gps")
			end
		end
	else
		npc_in_maze_action()
	end
end)

--【4】特殊中断情况
--> 王夫人哼了一声：“楼上是我曼佗罗山庄的藏书阁，你也想上？

add_trigger("walk_4","^\\s*(?:这个方向没有出路。|只见你脚下一滑，仰面摔了个大马趴，原来你踩了个西瓜皮。|你从山上滚了下来，只觉得浑身无处不疼。|你反应迅速，急忙双手抱头，身体蜷曲。眼前昏天黑地，顺着山路直滚了下去。|你一不小心脚下踏了个空，... 啊...！|你正在路上走着，忽见右首山谷中露出一点灯火！|王夫人哼了一声：“楼上是我)",function(params)
	local migong=var["migong"] or 0
	if migong==0 then
		var["wrong_way"]=var["wrong_way"] or 0
		var["wrong_way"]=var["wrong_way"]+1
	end
end)
add_trigger("walk_5","^\\s*你的动作还没有完成，不能移动。",function(params)
	local line2=line[2]
	if string.find(line2,"江南七侠「妙手书生」朱聪") then
		var["wrong_way"]=var["wrong_way"] or 0
		var["wrong_way"]=var["wrong_way"]+1
	end
end)
add_trigger("walk_6","^\\s*你把\\s+\"action\"\\s+设定为\\s+\"可以继续走了么\\.\\.\\.",function(params) --一步走模式
	if var.do_walk_one and var.do_walk_one ==  1 then
	    local port,id = tonumber(var.walk_port),var.walk_id
	    do_walk_one(port,id)
	end
end)

close_trigger("walk_1")
close_trigger("walk_2")
close_trigger("walk_3")
--你的动作还没有完成，不能移动。
--  江南七侠「妙手书生」朱聪(Zhu cong)
--你的动作还没有完成，不能移动。
--你的动作还没有完成，不能移动。


--****************************--
--			函数部分          --
--****************************--

--转换方向
var["dir"]={
	["West"]="w",
	["west"]="w",
	["East"]="e",
	["east"]="e",
	["South"]="s",
	["south"]="s",
	["North"]="n",
	["north"]="n",
	["Southeast"]="se",
	["southeast"]="se",
	["Southwest"]="sw",
	["southwest"]="sw",
	["Northeast"]="ne",
	["northeast"]="ne",
	["Northwest"]="nw",
	["northwest"]="nw",
	["Eastup"]="eu",
	["eastup"]="eu",
	["Westup"]="wu",
	["westup"]="wu",
	["Northup"]="nu",
	["northup"]="nu",
	["Southup"]="su",
	["southup"]="su",
	["Eastdown"]="ed",
	["eastdown"]="ed",
	["Westdown"]="wd",
	["westdown"]="wd",
	["Northdown"]="nd",
	["northdown"]="nd",
	["Southdown"]="sd",
	["southdown"]="sd",
	["Down"]="d",
	["down"]="d",
	["Up"]="u",
	["up"]="u",
	["N"]="n",
	["S"]="s",
	["W"]="w",
	["E"]="e",
}                

function changedir(dir)
	if var["dir"][dir] then
		return var["dir"][dir]
	else
		return dir
	end
end

--随机行走方向
function get_random_move(_direction) --east;west 中得到随机方向
	_direction=_direction or "east;south;west;north"
	local name=var["roomname"] or "none"
	local roomexit=var["roomexit"] or "none"
	if string.find("青城",name) then
		_direction="northwest;south"
	elseif string.find("吐谷浑伏俟城",name) then
		_direction="east;north;northwest;west"
	elseif string.find("草棚",name) then
		_direction=del_item(_direction,"northwest",";")
	elseif string.find("山脚",name) then
		_direction=del_item(_direction,"northup",";")
	elseif string.find("冷杉林",name) then
		_direction=del_item(_direction,"northeast",";")
	elseif string.find("山路",name) then
		_direction=del_item(_direction,"east",";")
	elseif string.find("村中心",name) then
		_direction=del_item(_direction,"northeast",";")
		_direction=del_item(_direction,"northwest",";")
	elseif string.find("钻天坡",name) then
		_direction=del_item(_direction,"westup",";")
	elseif string.find("凌云梯",name) then
		_direction=del_item(_direction,"northdown",";")
	elseif string.find("水塘",name) then
		_direction=del_item(_direction,"eastup",";")
	elseif string.find("舍利院",name) then
		_direction=del_item(_direction,"west",";")
	elseif string.find("黑石围子",name) then
		_direction=del_item(_direction,"north",";")
	elseif string.find("归云庄前",name) then
		_direction="north"
	elseif string.find("桑林外",name) then
		_direction="south"
	elseif string.find("农田口",name) then
		_direction="north"
	elseif string.find("采矿场入口",name) then
		_direction="northeast"
	elseif string.find("小山丘",name) then
		_direction=del_item(_direction,"westdown",";")
	elseif string.find("杏子林",name) then
		_direction="south"
	elseif string.find("柳林",name) then
		_direction="north"
	elseif string.find("针叶林",name) and string.find(roomexit,"southeast") then
		_direction="southeast|northeast"
	end
	--剔除迷宫方向
	_direction=del_item(_direction,"enter",";") --删除enter方向
	
	local _move,_exits="",{}
	for _k in string.gmatch(_direction..";","(.-);") do
		table.insert(_exits,_k)
	end
	if null(_exits) then
		local _default={"east","south","west","north","out"}
		_move=_default[math.random(4)]
	else
		local _num=table.maxn(_exits)
		_move=_exits[math.random(_num)]
	end
		return _move
end

--得到反方向
function get_reverse_move(_direction)
	local reverse_dir={
	["south"]="north",
	["north"]="south",
	["east"]="west",
	["west"]="east",
	["down"]="up",
	["up"]="down",
	["enter"]="out",
	["out"]="enter",
	["s"]="n",
	["n"]="s",
	["e"]="w",
	["w"]="e",
	["u"]="d",
	["d"]="u",
	
	["southup"]="northdown",
	["southdown"]="northup",
	["northup"]="southdown",
	["northdown"]="southup",
	["eastup"]="westdown",
	["eastdown"]="westup",
	["westup"]="eastdown",
	["westdown"]="eastup",
	["su"]="nd",
	["sd"]="nu",
	["nu"]="sd",
	["nd"]="su",
	["eu"]="wd",
	["ed"]="wu",
	["wd"]="eu",
	["wu"]="ed",

	["southeast"]="northwest",
	["southwest"]="northeast",
	["northeast"]="southwest",
	["northwest"]="southeast",
	["se"]="nw",
	["sw"]="ne",
	["ne"]="sw",
	["nw"]="se",

	}
	if reverse_dir[_direction] then
		_direction=reverse_dir[_direction]
		reverse_dir=nil
		return _direction
	else
		reverse_dir=nil
		return ""
	end
end
--g 某地方做某事
function g(_port,_action)
	var["port"]=_port
	function after_gps()
		function after_goto()
		_action()
		end
		exec("goto @port")
	end
	exec("gps")
end
--例如
--g(80,function()  send("xixi") end)
function get_duhe_port(roomname,roomexit) --得到当前渡河房间
	if roomname=="大渡口" and string.find(roomexit,"northwest") then --xxlz
		return 1922
	elseif roomname=="大渡口" and not string.find(roomexit,"northwest") and string.find(roomexit,"east") then --xxlz
		return 1921
	elseif roomname=="西夏渡口" and string.find(roomexit,"northwest") then
		return 1943
	elseif roomname=="西夏渡口" and string.find(roomexit,"southeast") then
		return 1908	
	elseif roomname=="陕晋渡口" and string.find(roomexit,"south") then
		return 738
	elseif roomname=="陕晋渡口" and string.find(roomexit,"north") then
		return 3407
	elseif roomname=="大渡口" and string.find(roomexit,"south") then
		return 208
	elseif roomname=="大渡口" and not string.find(roomexit,"northwest") and string.find(roomexit,"north") then
		return 3406
	end
end


add_alias("gtt", function()	
    -- 执行 look 命令触发房间信息更新
    exec('look;alias action 记录房间信息')
    
    -- 延迟执行以确保获取最新数据（根据网络延迟调整时间）
    alarm("show_room_info", 1, function()
        -- 获取原始房间信息字符串
        local room_info = var["roomzone"] .. var["roomname"] .. "|" .. var["roomnearby"] .. "|" .. var["roomexit"]
        
        -- 分割字符串并提取关键信息
        local parts = {}
        for part in string.gmatch(room_info, "[^|]+") do
            table.insert(parts, part)
        end
        
        -- 获取房间名字和ID
        local room_name = parts[1] or "未知名称"
        local room_id = var["ggps"] or "未知ID"
        
        -- 输出格式化信息
        echo("\n"..C.W.."房间名称："..C.G..room_name)
        echo("\n"..C.W.."房间编号："..C.G..room_id)
    end)
end)
Print("--- 加载模块: 行走 ---")
--job.lua 任务模块，一些通用属性

function close_all()
	if type(close_qqll_captain) == "function" then
		close_qqll_captain()
	end
	if type(close_qqll_member) == "function" then
		close_qqll_member()
	end

	if type(close_huashan_ask) == "function" then
		close_huashan_ask()
	end
	if type(close_huashan) == "function" then
		close_huashan()
	end
	if type(close_songxin_ask) == "function" then
		close_songxin_ask()
	end
	if type(close_songxin) == "function" then
		close_songxin()
	end
	if type(close_wudang) == "function" then
		close_wudang()
	end
	if type(close_wudang_ask) == "function" then
		close_wudang_ask()
	end
	if type(close_xueshan) == "function" then
		close_xueshan()
	end
	if type(close_xueshan_ask) == "function" then
		close_xueshan_ask()
	end
	if type(close_changlebang) == "function" then
		close_changlebang()
	end
	if type(close_changlebang_ask) == "function" then
		close_changlebang_ask()
	end
	if type(close_gaibang) == "function" then
		close_gaibang()
	end
	if type(close_gaibang_ask) == "function" then
		close_gaibang_ask()
	end
	if type(close_songshan) == "function" then
		close_songshan()
	end
	if type(close_songshan_ask) == "function" then
		close_songshan_ask()
	end
	if type(close_wuguan) == "function" then
		close_wuguan()
	end
	if type(close_xuncheng_ask) == "function" then
		close_xuncheng_ask()
	end
	if type(close_xuncheng) == "function" then
		close_xuncheng()
	end
	if type(close_xl_all) == "function" then
		close_xl_all()
	end
	if type(close_dashi_ask) == "function" then
		close_dashi_ask()
	end
	if type(close_do_job_hu) == "function" then
		close_do_job_hu()
	end

	if type(close_smy) == "function" then
		close_smy()
	end

	if type(close_swxy) == "function" then
		close_swxy()
	end

	if type(close_guanfu) == "function" then
		close_guanfu()
	end
end

function reset_search_list(room_list_check, search_list, job_zone, flag_job, sort)
	search_list = search_list or {}
	local remove_item, zone_only = {}, {}
	--[[
	if room_list_check[2118] then
		remove_item[2119]=true
		remove_item[2120]=true
		remove_item[2126]=true
	elseif room_list_check[2119] then
		remove_item[2118]=true
		remove_item[2120]=true
		remove_item[2126]=true
	elseif room_list_check[2120] then
		remove_item[2119]=true
		remove_item[2118]=true
		remove_item[2126]=true
	elseif room_list_check[2126] then
		remove_item[2119]=true
		remove_item[2120]=true
		remove_item[2118]=true
	elseif string.find(job_zone,"明教") then
		remove_item[2119]=true
		remove_item[2120]=true
		remove_item[2126]=true
	end
	]]
	if var["usefire"] and var["usefire"] == 1 then --买火折不去九老洞，丢了没法pfm了
		remove_item[1077] = true
	end
	if job_zone and string.find("|曼佗罗山庄|姑苏慕容|燕子坞|神龙岛|逍遥派|桃花岛|", "|" .. job_zone .. "|") then --这些地区剔除区域以外房间号
		for k, v in pairs(rooms) do
			if v["area"] == job_zone then
				zone_only[k] = true
			end
		end
	end

	local myexp = var["exp"] or 150000
	local newbie = var["newbie"] or 1 --默认新手做新手房间任务
	local newbie_rooms = "|" .. var["newbie_rooms"] .. "|"
	local search_list_new = {}
	if null(zone_only) == true then --独立区域没排除房间
		for k, v in pairs(search_list) do
			if not remove_item[v] then
				if newbie == 1 and not string.find(newbie_rooms, "|" .. v .. "|") then
					if myexp > 800000 then --经验高
						table.insert(search_list_new, v)
					end
				else
					table.insert(search_list_new, v)
				end
			end
		end
	else
		for k, v in pairs(search_list) do
			if not remove_item[v] and zone_only[v] then
				if not string.find(newbie_rooms, "|" .. v .. "|") then
					if newbie == 1 and myexp > 800000 then
						table.insert(search_list_new, v)
					end
				else
					table.insert(search_list_new, v)
				end
			end
		end
	end
	local _sort = sort or 0
	if _sort == 1 then
		table.sort(search_list_new) --重排
	end
	--	echo("\n"..C.m.."job.lua->get_job_rooms:return "..C.W..tostring(search_list_new))
	return search_list_new
end

function set_job(job)
	var["fight_escape"] = 0
	npc_in_maze_found = function() return nil end --迷宫发现npc
	npc_in_maze_action = function() return nil end
	var["not_wrong_way"] = nil              --默认非>>>出错
	del_trigger("huashan_xxdf")             --quest触发删除
	del_trigger("huashan_dgjj")             --quest 触发删除
	var["mygirl"] = 0                       --雪山自己的beauty
	var["fight_pause"] = 0
	var["fight_quit"] = 0
	var["fight_win"] = 0
	var["job_no_fight"] = 0
	send("set wimpycmd pfm\\hp")
	exec(var["skills_bei_nk"])
	send("alias pfm " .. var["pfm4"])
	var["killer_name"] = nil
	var["killer_id"] = nil
	var["killer2_name"] = nil
	var["killer2_id"] = nil
	var["killer_party"] = nil
	var["killer_skill"] = nil
	var["killer_level"] = nil


	if job == "xs" then --雪山任务设置
		require "job.xueshan"
		exec("set_job_xs")
	elseif job == "clb" then --雪山任务设置
		require "job.changlebang"
		exec("set_job_clb")
	elseif job == "xc" then --雪山任务设置
		require "job.xuncheng"
		exec("set_job_xc")
	elseif job == "gf" then --官府设置
		require "job.guanfu"
		exec("set_job_gf")
	elseif job == "wd" then --雪山任务设置
		require "job.wudang"
		exec("set_job_wd")
	elseif job == "gb" then --雪山任务设置
		require "job.gaibang"
		exec("set_job_gb")
	elseif job == "ss" then --雪山任务设置
		require "job.songshan"
		exec("set_job_ss")
	elseif job == "hs1" or job == "hs2" then --hs1任务设置
		require "job.huashan"
		exec("set_job_hs")
	elseif job == "xl" then --hs1任务设置
		require "job.xunluo"
		exec("set_job_xl")
	elseif job == "slhs" then --hs1任务设置
		require "job.husong"
		exec("set_job_slhs")
	elseif job == "dohs2" then
		var["flag_job"] = "hs2"
		set_wield_weapon("hs2")
		send("alias bei_skills " .. var["skills_bei_hs"])
	elseif job == "sx1" or job == "sx2" then --sx1任务设置
		-----------------------------------------------------------------------------
		--【送信1】--
		-----------------------------------------------------------------------------
		require "job.songxin"
		exec("set_job_sx1")
	elseif job == "smy" then
		require "job.songmoya"
		exec("set_job_smy")
	elseif job == "swxy" then
		require "job.xiangyangshouwei"
		exec("set_job_swxy")
	elseif job == "hb" then
		require "job.hubiao"
		exec("set_job_hubiao")
	elseif job == "xl" then
		require "job.xunluo"
		exec("set_job_xl")
	elseif job == "slhs" then
		require "job.husong"
		exec("set_job_slhs")
	elseif job == "qqll" then --七窍玲珑设置
		require "job.qqlljob"
		exec("set_job_qqll")
	elseif job == "zs" then
		require "job.zhuoshe"
		exec("set_job_zs")
	else
		require(job)
		--set_job_tdh
		exec("set_job_" .. job)
	end
end

add_alias("yun_powerup", function(params)
	local check = params[-1]
	add_alias("after_yun_powerup", function(params)
		exec(check)
	end)
	check_busy(function()
		check_busy2(function()
			local wudi = var["wudi"] or 0
			local skills_jifaid = var["skills_jifaid"] or {}
			if var["site"] and var["site"] == "养老院" then wudi = 0 end --不判断无敌le
			if wudi ~= 2 and skills_jifaid["force"] and string.find(skills_jifaid["force"], "dulong") then
				exec("unwield_weapon;wield_weapon;bei_skills")
				if var["exp"] and var["exp"] > 1000000 then
					send("yun wudi")
				end
				if var["yun_pfm"] and var["yun_pfm"] ~= "" then exec(var["yun_pfm"]) end
				if check == "none" then

				elseif check == "keepwalk" then
					alarm("powerup", 1, function()
						keepwalk()
					end)
				else
					check_busy(function()
						check_busy2(function()
							alarm("powerup", 1, function()
								exec("after_yun_powerup")
							end)
						end)
					end)
				end
			else
				exec("unwield_weapon;wield_weapon;bei_skills")
				if var["yun_pfm"] and var["yun_pfm"] ~= "" then exec(var["yun_pfm"]) end
				if check == "none" then

				elseif check == "keepwalk" then
					alarm("powerup", 1, function()
						keepwalk()
					end)
				else
					check_busy(function()
						check_busy2(function()
							alarm("powerup", 1, function()
								exec("after_yun_powerup")
							end)
						end)
					end)
				end
			end
		end)
	end)
end)

--设置武器装备
function set_wield_weapon(job)
	--var["ado"]=1
	if 1 == 1 or (var["ado"] and var["ado"] == 1) then --ado=1
		if var["useweapon"] and var["useweapon"] == 1 then --用武器--ado=1
			if job == "hs1" then                   --hs1 华山1任务
				send("alias bei_skills " .. var["skills_bei_hs"])
				if var["jobhs_useweapon"] and var["murongbishen"] and var["jobhs_useweapon"] == 1 and var["murongbishen"] ~= 1 then
					send("alias wield_weapon wield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				else
					send("alias wield_weapon unwield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				end
			elseif job == "hs2" or job == "dohs2" and 1 == 1 then --hs2 华山2任务
				send("alias bei_skills " .. var["skills_bei_hs"])
				if var["jobhs_useweapon"] and var["murongbishen"] and var["jobhs_useweapon"] == 1 and var["murongbishen"] ~= 1 then
					send("alias wield_weapon wield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				else
					send("alias wield_weapon unwield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				end
			elseif job == "wd" and 1 == 1 then --wd 武当任务
				send("alias bei_skills " .. var["skills_bei2"])
				if var["job2_useweapon"] and var["job2_useweapon"] == 1 then
					send("alias wield_weapon wield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				else
					send("alias wield_weapon unwield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				end
			elseif job == "slhs" and 1 == 1 then --slhs 少林护送
				send("alias bei_skills " .. var["skills_bei2"])
				if var["job2_useweapon"] and var["job2_useweapon"] == 1 then
					send("alias wield_weapon wield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				else
					send("alias wield_weapon unwield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				end
			elseif job == "qqll" and 1 == 1 then --qqll 七窍玲珑玉
				send("alias bei_skills " .. var["skills_bei2"])
				if var["job2_useweapon"] and var["job2_useweapon"] == 1 then
					send("alias wield_weapon wield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				else
					send("alias wield_weapon unwield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				end
			elseif job == "gb" and 1 == 1 then --gb任务
				send("alias bei_skills " .. var["skills_bei2"])
				if var["job2_useweapon"] and var["job2_useweapon"] == 1 then
					send("alias wield_weapon wield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				else
					send("alias wield_weapon unwield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				end
			elseif job == "tdh" and 1 == 1 then --tdh任务
				send("alias bei_skills " .. var["skills_bei_tdh"])
				if var["jobtdh_useweapon"] and var["jobtdh_useweapon"] == 1 then
					send("alias wield_weapon wield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				else
					send("alias wield_weapon unwield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				end
			elseif job == "smy" and 1 == 1 then --smy任务
				send("alias bei_skills " .. var["skills_bei_smy"])
				if var["jobsmy_useweapon"] and var["jobsmy_useweapon"] == 1 then
					send("alias wield_weapon wield " .. var["smy_weapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd wield " .. var["smy_weapon"] .. "\\hp\\pfm_backup")
				else
					send("alias wield_weapon unwield " .. var["smy_weapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd unwield " .. var["smy_weapon"] .. "\\hp\\pfm_backup")
				end
			elseif job == "swxy" and 1 == 1 then --smy任务
				send("alias bei_skills " .. var["skills_bei_smy"])
				if var["jobsmy_useweapon"] and var["jobsmy_useweapon"] == 1 then
					send("alias wield_weapon wield " .. var["smy_weapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd wield " .. var["smy_weapon"] .. "\\hp\\pfm_backup")
				else
					send("alias wield_weapon unwield " .. var["smy_weapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd unwield " .. var["smy_weapon"] .. "\\hp\\pfm_backup")
				end
			elseif job == "gf" and 1 == 1 then --gf任务
				send("alias bei_skills " .. var["skills_bei2"])
				if var["job2_useweapon"] and var["job2_useweapon"] == 1 then
					send("alias wield_weapon wield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				else
					send("alias wield_weapon unwield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				end
			elseif job == "ss" and var["songshan_type"] and var["songshan_type"] == "杀" then --ss杀人
				send("alias bei_skills " .. var["skills_bei2"])
				if var["job2_useweapon"] and var["job2_useweapon"] == 1 then
					send("alias wield_weapon wield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				else
					send("alias wield_weapon unwield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				end
			elseif job == "ss" and var["songshan_type"] and var["songshan_type"] == "请" and var["skills_bei4"] and var["skills_bei4"] ~= "" and var["job3_useweapon"] then --ss请人
				send("alias bei_skills " .. var["skills_bei4"])
				if var["job3_useweapon"] and var["job3_useweapon"] == 1 then
					send("alias wield_weapon wield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				else
					send("alias wield_weapon unwield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				end
			elseif job == "sx1" and 1 == 1 then --sx1送信1任务
				send("alias bei_skills " .. var["skills_bei1"])
				if var["job1_useweapon"] and var["murongbishen"] and var["job1_useweapon"] == 1 and var["murongbishen"] ~= 1 then
					send("alias wield_weapon wield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				else
					send("alias wield_weapon unwield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				end
			elseif job == "zs" and 1 == 1 then --zhuoshe
				send("alias bei_skills " .. var["skills_bei1"])
				if var["job1_useweapon"] and var["murongbishen"] and var["job1_useweapon"] == 1 and var["murongbishen"] ~= 1 then
					send("alias wield_weapon wield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				else
					send("alias wield_weapon unwield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				end
			elseif job == "sx2" and 1 == 1 then --sx1送信2任务
				send("alias bei_skills " .. var["skills_bei2"])
				if var["job2_useweapon"] and var["job2_useweapon"] == 1 then
					send("alias wield_weapon wield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				else
					send("alias wield_weapon unwield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				end
			elseif job == "xs" and 1 == 1 then --雪山任务
				send("alias bei_skills " .. var["skills_bei2"])
				if var["job2_useweapon"] and var["job2_useweapon"] == 1 then
					send("alias wield_weapon wield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				else
					send("alias wield_weapon unwield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				end
			elseif job == "clb" and 1 == 1 then --长乐帮2
				send("alias bei_skills " .. var["skills_bei2"])
				if var["job2_useweapon"] and var["job2_useweapon"] == 1 then
					send("alias wield_weapon wield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				else
					send("alias wield_weapon unwield " .. var["myweapon"])
					send("alias unwield_weapon unwield " ..
					var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
					send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
				end
			end
		else --不用武器--ado=1
			send("alias wield_weapon unwield " .. var["myweapon"] .. ";unwield " .. var["smy_weapon"])
			send("alias unwield_weapon unwield " ..
			var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
			send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
		end
	else --ado=0
		--预留
	end
end

function set_prepare(job)
	--if var["useweapon"] and var["useweapon"]==1 and var["myweapon"] and var["myweapon"]~="" and var["myweapon"]~=" " then --用武器
	if var["useweapon"] and var["useweapon"] == 1 then
		if job == "hs1" then --hs1 华山1任务
			send("alias bei_skills " .. var["skills_bei_hs"])
			if var["jobhs_useweapon"] and var["murongbishen"] and var["jobhs_useweapon"] == 1 and var["murongbishen"] ~= 1 then
				send("alias wield_weapon wield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			else
				send("alias wield_weapon unwield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			end
		elseif job == "hs2" or job == "dohs2" then --hs2 华山2任务
			send("alias bei_skills " .. var["skills_bei_hs"])
			if var["jobhs_useweapon"] and var["murongbishen"] and var["jobhs_useweapon"] == 1 and var["murongbishen"] ~= 1 then
				send("alias wield_weapon wield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			else
				send("alias wield_weapon unwield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			end
		elseif job == "wd" then --wd 武当任务
			send("alias bei_skills " .. var["skills_bei2"])
			if var["job2_useweapon"] and var["job2_useweapon"] == 1 then
				send("alias wield_weapon wield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			else
				send("alias wield_weapon unwield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			end
		elseif job == "gb" then --gb任务
			send("alias bei_skills " .. var["skills_bei2"])
			if var["job2_useweapon"] and var["job2_useweapon"] == 1 then
				send("alias wield_weapon wield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			else
				send("alias wield_weapon unwield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			end
		elseif job == "tdh" then --tdh任务
			send("alias bei_skills " .. var["skills_bei_tdh"])
			if var["jobtdh_useweapon"] and var["jobtdh_useweapon"] == 1 then
				send("alias wield_weapon wield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			else
				send("alias wield_weapon unwield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			end
		elseif job == "smy" then --smy任务
			send("alias bei_skills " .. var["skills_bei_smy"])
			if var["jobsmy_useweapon"] and var["jobsmy_useweapon"] == 1 then
				send("alias wield_weapon wield " .. var["smy_weapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd wield " .. var["smy_weapon"] .. "\\hp\\pfm_backup")
			else
				send("alias wield_weapon unwield " .. var["smy_weapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd unwield " .. var["smy_weapon"] .. "\\hp\\pfm_backup")
			end
		elseif job == "swxy" then --swxy任务
			send("alias bei_skills " .. var["skills_bei_smy"])
			if var["jobsmy_useweapon"] and var["jobsmy_useweapon"] == 1 then
				send("alias wield_weapon wield " .. var["smy_weapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd wield " .. var["smy_weapon"] .. "\\hp\\pfm_backup")
			else
				send("alias wield_weapon unwield " .. var["smy_weapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd unwield " .. var["smy_weapon"] .. "\\hp\\pfm_backup")
			end
		elseif job == "gf" then --gf任务
			send("alias bei_skills " .. var["skills_bei2"])
			if var["job2_useweapon"] and var["job2_useweapon"] == 1 then
				send("alias wield_weapon wield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			else
				send("alias wield_weapon unwield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			end
		elseif job == "ss" and var["songshan_type"] and var["songshan_type"] == "杀" then --ss杀人
			send("alias bei_skills " .. var["skills_bei2"])
			if var["job2_useweapon"] and var["job2_useweapon"] == 1 then
				send("alias wield_weapon wield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			else
				send("alias wield_weapon unwield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			end
		elseif job == "ss" and var["songshan_type"] and var["songshan_type"] == "请" and var["skills_bei4"] and var["skills_bei4"] ~= "" and var["job3_useweapon"] then --ss请人
			send("alias bei_skills " .. var["skills_bei4"])
			if var["job3_useweapon"] and var["job3_useweapon"] == 1 then
				send("alias wield_weapon wield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			else
				send("alias wield_weapon unwield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			end
		elseif job == "sx1" then --sx1送信1任务
			send("alias bei_skills " .. var["skills_bei1"])
			if var["job1_useweapon"] and var["murongbishen"] and var["job1_useweapon"] == 1 and var["murongbishen"] ~= 1 then
				send("alias wield_weapon wield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			else
				send("alias wield_weapon unwield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			end
		elseif job == "zs" then --zhuoshe
			send("alias bei_skills " .. var["skills_bei1"])
			if var["job1_useweapon"] and var["murongbishen"] and var["job1_useweapon"] == 1 and var["murongbishen"] ~= 1 then
				send("alias wield_weapon wield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			else
				send("alias wield_weapon unwield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			end
		elseif job == "sx2" then --sx1送信2任务
			send("alias bei_skills " .. var["skills_bei2"])
			if var["job2_useweapon"] and var["job2_useweapon"] == 1 then
				send("alias wield_weapon wield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			else
				send("alias wield_weapon unwield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			end
		elseif job == "xs" then --雪山任务
			send("alias bei_skills " .. var["skills_bei2"])
			if var["job2_useweapon"] and var["job2_useweapon"] == 1 then
				send("alias wield_weapon wield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			else
				send("alias wield_weapon unwield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			end
		elseif job == "clb" then --长乐帮2
			send("alias bei_skills " .. var["skills_bei2"])
			if var["job2_useweapon"] and var["job2_useweapon"] == 1 then
				send("alias wield_weapon wield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd wield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			else
				send("alias wield_weapon unwield " .. var["myweapon"])
				send("alias unwield_weapon unwield " ..
				var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
				send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
			end
		end
	else --不用武器	
		if job == "hs1" then --hs1 华山1任务
			send("alias bei_skills " .. var["skills_bei_hs"])
		elseif job == "hs2" or job == "dohs2" then --hs2 华山2任务
			send("alias bei_skills " .. var["skills_bei_hs"])
		elseif job == "wd" then --wd 武当任务
			send("alias bei_skills " .. var["skills_bei2"])
		elseif job == "gb" then --gb任务
			send("alias bei_skills " .. var["skills_bei2"])
		elseif job == "tdh" then --tdh任务
			send("alias bei_skills " .. var["skills_bei_tdh"])
		elseif job == "smy" then --smy任务
			send("alias bei_skills " .. var["skills_bei_smy"])
		elseif job == "swxy" then --smy任务
			send("alias bei_skills " .. var["skills_bei_smy"])
		elseif job == "gf" then --gf任务
			send("alias bei_skills " .. var["skills_bei2"])
		elseif job == "ss" and var["songshan_type"] and var["songshan_type"] == "杀" then --ss杀人
			send("alias bei_skills " .. var["skills_bei2"])
		elseif job == "ss" and var["songshan_type"] and var["songshan_type"] == "请" and var["skills_bei4"] and var["skills_bei4"] ~= "" and var["job3_useweapon"] then --ss请人
			send("alias bei_skills " .. var["skills_bei4"])
		elseif job == "sx1" then --sx1送信1任务
			send("alias bei_skills " .. var["skills_bei1"])
		elseif job == "zs" then --zhuoshe
			send("alias bei_skills " .. var["skills_bei1"])
		elseif job == "sx2" then --sx1送信2任务
			send("alias bei_skills " .. var["skills_bei2"])
		elseif job == "xs" then --雪山任务
			send("alias bei_skills " .. var["skills_bei2"])
		elseif job == "clb" then --长乐帮2
			send("alias bei_skills " .. var["skills_bei2"])
		end

		if var["myweapon"] and var["myweapon"] ~= "" and var["myweapon"] ~= " " then
			send("alias wield_weapon unwield " .. var["myweapon"])
			send("alias unwield_weapon unwield " ..
			var["myweapon"] .. ";unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
			send("alias fight_reset set wimpycmd unwield " .. var["myweapon"] .. "\\hp\\pfm_backup")
		else
			send("alias wield_weapon unwield " .. var["smy_weapon"] .. ";unwield " .. var["myweapon"])
			send("alias unwield_weapon unwield " .. var["smy_weapon"] .. ";unwield " .. var["weapon"])
			send("alias fight_reset set wimpycmd hp\\pfm_backup")
		end
	end
end

--guanfu job分解名称
function break_zone_room(_zoneroom) --分解zone 和 room
	if type(_zoneroom) ~= "string" then
		return "", "", nil
	else
		local room_number = nil
		if change_zoneroom[_zoneroom] then
			_zoneroom = change_zoneroom[_zoneroom] --把一些夜间关门房间变一下room.lua
		end
		local _zone, _room
		if string.find("|山脚下|莎萝坪|山洪瀑布|青柯坪|千尺幢|百尺峡|老君沟|猢狲愁|苍龙岭|镇岳宫|舍身崖|朝阳峰|玉女峰|玉女祠|祭坛|瀑布|小山路|思过崖|思过崖洞口|山路|小溪|台阶|前厅|侧廊|练武场|药房|饭厅|后堂|寝室|正气堂|男休息室|书院|剑房|树林|松树林|空地|山涧|茅屋|石屋|玉泉院|女休息室|", "|" .. _zoneroom .. "|") then --华山
			_zone = "华山"
			_room = _zoneroom
		elseif string.find("|黄土路|南村口|碎石路|民房|铁匠铺|村中心|酒肆|玄坛庙|杂货铺|东村口|菜地|", "|" .. _zoneroom .. "|") then --华山村
			_zone = "华山村"
			_room = _zoneroom
		elseif _zoneroom == "土路" or _zoneroom == "大道" or _zoneroom == "大驿道" or _zoneroom == "驿道" or _zoneroom == "燕子洞" or _zoneroom == "土林" or _zoneroom == "梅里雪山" or _zoneroom == "林间道" or _zoneroom == "山路" or _zoneroom == "终南广场" or _zoneroom == "树林" or _zoneroom == "小路" or _zoneroom == "青石大道" or _zoneroom == "烟雨楼" or _zoneroom == "石峡子" or _zoneroom == "大沙漠" or _zoneroom == "青石官道" or _zoneroom == "山坡" or _zoneroom == "黄土路" or _zoneroom == "藏边土路" or _zoneroom == "湖边小路" or _zoneroom == "小径" then
			if _zoneroom == "土路" then --"平定州" --"牛家村"--"苏州城"
				_zone = "长安城"
				_room = _zoneroom
			elseif _zoneroom == "大道" then --"兰州城" "大理城北"
				_zone = "成都城"
				_room = _zoneroom
			elseif _zoneroom == "大驿道" then --"沧州城" --泰山
				_zone = "扬州城"
				_room = _zoneroom
			elseif _zoneroom == "大驿道" or _zoneroom == "驿道" then
				_zone = "沧州城"
				_room = _zoneroom
			elseif _zoneroom == "燕子洞" then
				_zone = "大理城东"
				_room = _zoneroom
			elseif _zoneroom == "大道" then
				_zone = "大理城北"
				_room = _zoneroom
			elseif _zoneroom == "土林" then
				_zone = "大理城南"
				_room = _zoneroom
			elseif _zoneroom == "梅里雪山" then
				_zone = "大理城西"
				_room = _zoneroom
			elseif _zoneroom == "林间道" then
				_zone = "佛山镇"
				_room = _zoneroom
			elseif _zoneroom == "山路" then --"襄阳郊外"
				_zone = "福州城"
				_room = _zoneroom
			elseif _zoneroom == "终南广场" then
				_zone = "终南山"
				_room = _zoneroom
			elseif _zoneroom == "树林" then
				_zone = "黄河流域"
				_room = _zoneroom
			elseif _zoneroom == "小路" then
				_zone = "回疆草原"
				_room = _zoneroom
			elseif _zoneroom == "土路" then
				_zone = "平定州"
				_room = _zoneroom
			elseif _zoneroom == "青石大道" then
				_zone = "杭州城"
				_room = _zoneroom
			elseif _zoneroom == "烟雨楼" then
				_zone = "嘉兴城"
				_room = _zoneroom
			elseif _zoneroom == "大道" then
				_zone = "兰州城"
				_room = _zoneroom
			elseif _zoneroom == "石峡子" then
				_zone = "兰州城"
				_room = _zoneroom
			elseif _zoneroom == "大沙漠" then
				_zone = "明教"
				_room = _zoneroom
			elseif _zoneroom == "青石官道" then
				_zone = "宁波城"
				_room = _zoneroom
			elseif _zoneroom == "山坡" then
				_zone = "嵩山少林"
				_room = _zoneroom
			elseif _zoneroom == "青石官道" then
				_zone = "宁波城"
				_room = _zoneroom
			elseif _zoneroom == "黄土路" then
				_zone = "武当山"
				_room = _zoneroom
			elseif _zoneroom == "藏边土路" then
				_zone = "大雪山"
				_room = _zoneroom
			elseif _zoneroom == "湖边小路" then
				_zone = "大雪山"
				_room = _zoneroom
			elseif _zoneroom == "小径" then
				_zone = "武当山"
				_room = _zoneroom
			end
		elseif string.find("大理城东门|大理城西门|大理城南门|大理城东大街|大理城西大街|大理城南大街", _zoneroom) then --大理城南
			_zone = "大理城"
			_room = string.sub(_zoneroom, 7)
		else --用一下正则，太多了
			_zone, _room = rex.match(_zoneroom,
				"^((?:曼佗罗山庄|试剑山庄|极乐世界|神州中原|中原神州|丐帮分舵|姑苏慕容|武当后山|嵩山少林|莆田少林|黄河流域|镇南王府|大理王府|大理皇宫|襄阳郊外|中原神州|神州中原|大理城西|大理城南|大理城东|大理国|燕子坞|神龙岛|逍遥派|全真教|全真派|无量山|华山村|终南山|长乐帮|长安城|玉虚观|伊犁城|扬州城|星宿海|襄阳城|武当山|铁掌山|天龙寺|血刀门|摩天崖|桃源县|桃花岛|塘沽城|平定州|牛家村|宁波城|南阳城|苏州城|柳宗镇|兰州城|昆仑山|白驼山|绝情谷|嘉兴城|蝴蝶谷|黑木崖|杭州城|归云庄|福州城|佛山镇|峨嵋山|大雪山|大理城|大草原|成都城|沧州城|摩天崖|侠客岛|逍遥派|全真教|伊犁|逍遥|中原|神州|萧府|天山|泰山|嵩山|明教|苗疆|梅庄|回疆|华山|恒山|衡山|丐帮|蒙古|扬州|襄阳|塘沽|苏州|宁波|南阳|兰州|嘉兴|杭州|古墓|福州|佛山|成都|长安|沧州|京城))(.+)")
		end
		--镇南王府
		--伊犁
		--扬州
		--再次改名比如中原啥的
		if change_zoneroom[_zone] then
			_zone = change_zoneroom[_zone] -- _zone
		end
		--中原神州
		if type(_zone) == "nil" then
			return "", "", room_number
		else
			return _zone, _room, room_number
		end
	end
end

--从npc名字得到id
function get_id(name, party, f) --得到name的id
	local id = ""
	--local f=f or 0
	if data_system_npc[name] then --系统npc
		id = data_system_npc[name]
	else
		local n1 = string.sub(name, 1, 2)
		local n2 = string.sub(name, 3, 4)
		local n3 = string.sub(name, 5, 6)
		local n4 = string.sub(name, 7, 8)
		local n12 = string.sub(name, 1, 4)
		local n23 = string.sub(name, 3, 6)
		local n34 = string.sub(name, 5, 8)
		local n = string.sub(name, -2)
		local p = var["killer_party"] or "none"
		if party ~= nil then
			p = party
		end
		--echo(C.W..p)
		local l = string.len(name)
		local data_name_n1 = data_name[n1] or ""
		local data_name_n2 = data_name[n2] or ""
		local data_name_n3 = data_name[n3] or ""
		local data_name_n4 = data_name[n4] or ""
		local data_name_n12 = data_name[n12] or ""
		local data_name_n23 = data_name[n23] or ""
		local data_name_n34 = data_name[n34] or ""
		if l == 8 then
			if string.find(n, "风") and string.find(p, "桃花岛") then
				if data_name_fuxing[n12] then
					--			id=data_name[n1]..data_name[n2]..data_name[n3].." "..data_name[n4]
					id = data_name_n12 .. data_name_n3 .. " " .. data_name_n4
				elseif data_name_fuxing[n23] then
					id = data_name_n1 .. data_name_n23 .. " " .. data_name_n4
				else
					id = data_name_n1 .. data_name_n2 .. data_name_n3 .. " " .. data_name_n4
				end
			else
				if data_name_fuxing[n12] and data_name_fuxing[n34] then
					id = data_name_n12 .. " " .. data_name_n34
				elseif data_name_fuxing[n12] then
					id = data_name_n12 .. " " .. data_name_n3 .. data_name_n4
				elseif data_name_fuxing[n23] then
					id = data_name_n1 .. " " .. data_name_n23 .. data_name_n4
				elseif data_name_fuxing[n34] then
					id = data_name_n1 .. " " .. data_name_n2 .. data_name_n34
				else
					id = data_name_n1 .. data_name_n2 .. " " .. data_name_n3 .. data_name_n4
				end
			end
		elseif l == 6 then
			if string.find(n, "风") and string.find(p, "桃花岛") then
				if data_name_fuxing[n12] then
					id = data_name_n12 .. " " .. data_name_n3
				else
					id = data_name_n1 .. data_name_n2 .. " " .. data_name_n3
				end
			else
				if data_name_fuxing[n12] then
					id = data_name_n12 .. " " .. data_name_n3
				elseif data_name_fuxing[n23] then
					id = data_name_n1 .. " " .. data_name_n23
				else
					id = data_name_n1 .. " " .. data_name_n2 .. data_name_n3
				end
			end
		elseif l == 4 then
			if data_name_fuxing[n12] then
				id = data_name_n12
			else
				id = data_name_n1 .. " " .. data_name_n2
			end
		elseif l == 2 then
			id = data_name_n1
		end
	end
	return id
end

add_alias("set_danger_list", function(params)
	local danger = var["danger_list"] or {}
	local name = params[-1]
	local in_table = 0
	for k, v in pairs(danger) do
		if string.find(name, v) then
			in_table = 1
		end
	end
	if in_table == 0 and name then
		danger[10] = nil
		table.insert(danger, 1, name)
		var["danger_list"] = danger
	end
end)
--[[
add_alias("get_danger_list",function(params)
local d=var["danger_list"] or {}
for k,v in pairs(d) do
echo(C.W..k..":"..v)
end
end)
]]



--名字对应id的数据库
data_name = { ["芝"] = "zhi", ["时"] = "shi", ["润"] = "run", ["光"] = "guang", ["标"] = "biao", ["田"] = "tian", ["杜"] = "du",
	["若"] = "ruo", ["彩"] = "cai", ["徐"] = "xu", ["郝"] = "hao", ["谷梁"] = "guliang", ["禄"] = "lu", ["虎"] = "hu", ["支"] =
"zhi", ["许"] = "xu", ["伟"] = "wei", ["度"] = "du", ["表"] = "biao", ["蔡"] = "cai", ["瑛"] = "ying", ["羲"] = "xi", ["法"] =
"fa", ["渡"] = "du", ["超"] = "chao", ["陆"] = "lu", ["淳于"] = "chunyu", ["俊"] = "jun", ["峥"] = "zheng", ["造"] = "zao", ["浩"] =
"hao", ["严"] = "yan", ["朝伟"] = "chaowei", ["旭"] = "xu", ["端"] = "duan", ["史"] = "shi", ["佰赏"] = "baishang", ["欧阳"] =
"ouyang", ["伍藏"] = "wuzang", ["凝"] = "ning", ["未"] = "wei", ["萍"] = "ping", ["宁"] = "ning", ["麟"] = "lin", ["玲"] = "ling",
	["归"] = "gui", ["朝"] = "chao", ["吕"] = "lv", ["九"] = "jiu", ["平"] = "ping", ["花"] = "hua", ["黛"] = "dai", ["冶"] =
"ye", ["端木"] = "duanmu", ["皇甫"] = "huangpu", ["之"] = "zhi", ["斌"] = "bin", ["邵"] = "shao", ["延"] = "yan", ["段"] = "duan",
	["三"] = "san", ["拓"] = "tuo", ["培"] = "pei", ["华"] = "hua", ["断"] = "duan", ["言"] = "yan", ["樊"] = "fan", ["令狐"] =
"linghu", ["铁"] = "tie", ["雕"] = "diao", ["式"] = "shi", ["和"] = "he", ["宾"] = "bin", ["阎"] = "yan", ["单于"] = "chanyu",
	["叶"] = "ye", ["魏"] = "wei", ["何"] = "he", ["乐正"] = "yuezheng", ["士"] = "shi", ["开"] = "kai", ["合"] = "he", ["桂"] =
"gui", ["凡"] = "fan", ["赵"] = "zhao", ["凌"] = "ling", ["舌"] = "she", ["轩"] = "xuan", ["公西"] = "gongxi", ["世"] = "shi",
	["嵘"] = "rong", ["佩"] = "pei", ["化"] = "hua", ["藏"] = "zang", ["僖"] = "xi", ["沛"] = "pei", ["律"] = "lv", ["尉"] =
"wei", ["破"] = "po", ["凯"] = "kai", ["曾"] = "zeng", ["茅"] = "mao", ["轩辕"] = "xuanyuan", ["一"] = "yi", ["魄"] = "po", ["金"] =
"jin", ["河"] = "he", ["范"] = "fan", ["窦"] = "dou", ["富城"] = "fucheng", ["玄"] = "xuan", ["卫"] = "wei", ["焘"] = "tao",
	["毛"] = "mao", ["曹"] = "cao", ["千"] = "qian", ["艳"] = "yan", ["姗"] = "shan", ["令"] = "ling", ["赫"] = "he", ["颛孔"] =
"zhuankong", ["温"] = "wen", ["趾"] = "zhi", ["燕"] = "yan", ["睿"] = "rui", ["欢"] = "huan", ["鹤"] = "he", ["策"] = "ce",
	["濮阳"] = "puyang", ["长孙"] = "zhangsun", ["文"] = "wen", ["谦"] = "qian", ["茂"] = "mao", ["帅"] = "shuai", ["彭"] = "peng",
	["贺"] = "he", ["郭"] = "guo", ["段千"] = "duanqian", ["闻"] = "wen", ["乾"] = "qian", ["菊"] = "ju", ["国"] = "guo", ["芳"] =
"fang", ["申"] = "shen", ["微生"] = "weisheng", ["志"] = "zhi", ["康"] = "kang", ["戟"] = "ji", ["方"] = "fang", ["巫马"] = "wuma",
	["英"] = "ying", ["进"] = "jin", ["佘"] = "she", ["钱"] = "qian", ["陈"] = "chen", ["孟达"] = "mengda", ["过"] = "guo", ["多"] =
"duo", ["刘"] = "liu", ["深"] = "shen", ["前"] = "qian", ["举"] = "ju", ["哈"] = "ha", ["晋"] = "jin", ["潜"] = "qian", ["朋"] =
"peng", ["流"] = "liu", ["逵"] = "kui", ["司马"] = "sima", ["珍"] = "zhen", ["仪"] = "yi", ["鹏"] = "peng", ["伦"] = "lun", ["水"] =
"shui", ["柳"] = "liu", ["颛"] = "zhuan", ["亢"] = "kang", ["六"] = "liu", ["翁"] = "weng", ["城"] = "cheng", ["祝"] = "zhu",
	["真"] = "zhen", ["普"] = "pu", ["龙"] = "long", ["福"] = "fu", ["沈"] = "shen", ["川"] = "chuan", ["成"] = "cheng", ["艾"] =
"ai", ["澹台"] = "tantai", ["赫连"] = "helian", ["学友"] = "xueyou", ["德华"] = "dehua", ["智"] = "zhi", ["荧"] = "ying", ["婉"] =
"wan", ["顺"] = "shun", ["爱"] = "ai", ["甫"] = "pu", ["公孙"] = "gongsun", ["杨"] = "yang", ["万"] = "wan", ["程"] = "cheng",
	["夏候"] = "xiahou", ["小春"] = "xiaochun", ["罗"] = "luo", ["飞"] = "fei", ["汝鄢"] = "ruyan", ["虹"] = "hong", ["汪"] =
"wang", ["柯"] = "ke", ["黄"] = "huang", ["澄"] = "chen", ["公良"] = "gongliang", ["盈"] = "ying", ["诚"] = "cheng", ["安"] =
"an", ["王"] = "wang", ["曼玉"] = "manyu", ["寿"] = "shou", ["美"] = "mei", ["洪"] = "hong", ["褚"] = "zhu", ["戚"] = "qi", ["羊"] =
"yang", ["生"] = "sheng", ["治"] = "zhi", ["颖"] = "ying", ["斯"] = "si", ["坤"] = "kun", ["宏"] = "hong", ["师空"] = "shikong",
	["七"] = "qi", ["昆"] = "kun", ["阳"] = "yang", ["巫"] = "wu", ["皇"] = "huang", ["南郭"] = "nanguo", ["骧驷"] = "xiangsi",
	["振"] = "zhen", ["强"] = "qiang", ["吉"] = "ji", ["红"] = "hong", ["罡"] = "gang", ["太叔"] = "taishu", ["闻人"] = "wenren",
	["思"] = "si", ["奚"] = "xi", ["漆"] = "qi", ["黛玉"] = "daiyu", ["忠"] = "zhong", ["媛"] = "yuan", ["可"] = "ke", ["娟"] =
"juan", ["费"] = "fei", ["门"] = "men", ["楚晋"] = "chujin", ["涂钦"] = "tuqin", ["易"] = "yi", ["婷"] = "ting", ["芬"] = "fen",
	["钟"] = "zhong", ["司"] = "si", ["克"] = "ke", ["霞"] = "xia", ["威"] = "wei", ["阔"] = "kuo", ["厚"] = "hou", ["亿"] = "yi",
	["奇"] = "qi", ["蒙"] = "meng", ["候"] = "hou", ["复"] = "fu", ["迟"] = "chi", ["诸葛"] = "zhuge", ["傅"] = "fu", ["灰"] =
"hui", ["后"] = "hou", ["敖"] = "ao", ["昝"] = "jiu", ["夹谷"] = "jiagu", ["西门"] = "ximen", ["付"] = "fu", ["春"] = "chun",
	["驰"] = "chi", ["乔"] = "qiao", ["鄢"] = "yan", ["呼"] = "hu", ["拓哉"] = "tuozai", ["四"] = "si", ["仲"] = "zhong", ["逸"] =
"yi", ["崎"] = "qi", ["辉"] = "hui", ["昌"] = "chang", ["翱"] = "ao", ["墨"] = "mo", ["马"] = "ma", ["叔"] = "shu", ["柳任"] =
"liuren", ["权"] = "quan", ["猛"] = "meng", ["吴"] = "wu", ["舒"] = "shu", ["耳"] = "er", ["父"] = "fu", ["舟"] = "zhou", ["小"] =
"xiao", ["淑"] = "shu", ["傲"] = "ao", ["齐"] = "qi", ["嫣"] = "yan", ["孝"] = "xiao", ["武"] = "wu", ["泉"] = "quan", ["孟"] =
"meng", ["周"] = "zhou", ["正"] = "zheng", ["淳"] = "chun", ["呼延"] = "huyan", ["青霞"] = "qingxia", ["五"] = "wu", ["永"] =
"yong", ["富"] = "fu", ["夏"] = "xia", ["政"] = "zheng", ["东郭"] = "dongguo", ["宇文"] = "yuwen", ["宝钗"] = "baochai", ["二"] =
"er", ["意"] = "yi", ["台"] = "tai", ["松"] = "song", ["空"] = "kong", ["肖"] = "xiao", ["祁"] = "qi", ["胡"] = "hu", ["润发"] =
"runfa", ["勇"] = "yong", ["毅"] = "yi", ["泰"] = "tai", ["谋"] = "mou", ["麦"] = "mai", ["谯"] = "qiao", ["赖"] = "lai", ["东方"] =
"dongfang", ["忆"] = "yi", ["起"] = "qi", ["慧"] = "hui", ["牟"] = "mou", ["孔"] = "kong", ["狐"] = "hu", ["裕"] = "yu", ["义"] =
"yi", ["迈"] = "mai", ["均"] = "jun", ["宝"] = "bao", ["伍"] = "wu", ["太"] = "tai", ["楚红"] = "chuhong", ["益"] = "yi", ["惠"] =
"hui", ["丰"] = "feng", ["尉迟"] = "yuchi", ["豫"] = "yu", ["封"] = "feng", ["丹"] = "dan", ["宋"] = "song", ["启"] = "qi", ["军"] =
"jun", ["米"] = "mi", ["单"] = "shan", ["八"] = "ba", ["尤"] = "you", ["宙"] = "zhou", ["贤"] = "xian", ["满"] = "man", ["兰"] =
"lan", ["峻"] = "jun", ["钗"] = "chai", ["鲍"] = "bao", ["钦"] = "qin", ["锋"] = "feng", ["巴"] = "ba", ["异"] = "yi", ["曼"] =
"man", ["健"] = "jian", ["风"] = "feng", ["元"] = "yuan", ["珠"] = "zhu", ["务"] = "wu", ["济"] = "ji", ["功"] = "gong", ["秦"] =
"qin", ["苏"] = "su", ["琴"] = "qin", ["弃"] = "qi", ["剑"] = "jian", ["恭"] = "gong", ["袁"] = "yuan", ["申屠"] = "shentu",
	["勤"] = "qin", ["龚"] = "gong", ["慕"] = "mu", ["左丘"] = "zuoqiu", ["潭"] = "tan", ["芹"] = "qin", ["群"] = "qun", ["木"] =
"mu", ["计"] = "ji", ["朱"] = "zhu", ["甘"] = "gan", ["冯"] = "feng", ["熙"] = "xi", ["然"] = "ran", ["立"] = "li", ["有"] =
"you", ["谭"] = "tan", ["仇"] = "qiu", ["辕"] = "yuan", ["柏芝"] = "baizhi", ["友"] = "you", ["公"] = "gong", ["霸"] = "ba",
	["谈"] = "tan", ["诸"] = "zhu", ["宗政"] = "zongzheng", ["殷"] = "yin", ["西"] = "xi", ["坦"] = "tan", ["牧"] = "mu", ["琅"] =
"lang", ["建"] = "jian", ["奉"] = "feng", ["宫"] = "gong", ["贝"] = "bei", ["星驰"] = "xingchi", ["圆"] = "yuan", ["凤"] = "feng",
	["当"] = "dang", ["穆"] = "mu", ["阎法"] = "yanfa", ["青"] = "qing", ["力"] = "li", ["白"] = "bai", ["姜"] = "jiang", ["驷"] =
"si", ["公冶"] = "gongye", ["柏"] = "bo", ["夫"] = "fu", ["郎"] = "lang", ["纪"] = "ji", ["百"] = "bai", ["东门"] = "dongmen",
	["仲孙"] = "zhongsun", ["熙凤"] = "xifeng", ["子"] = "zi", ["朗"] = "lang", ["江"] = "jiang", ["嘉"] = "jia", ["苗"] = "miao",
	["缑"] = "gou", ["自"] = "zi", ["宪"] = "xian", ["谢"] = "xie", ["佰"] = "bai", ["瑜"] = "yu", ["寅"] = "yin", ["清"] =
"qing", ["莲"] = "lian", ["宽"] = "kuan", ["汤"] = "tang", ["桑"] = "sang", ["蒋"] = "jiang", ["夹"] = "jia", ["卫健"] =
"weijian", ["纳"] = "na", ["佳"] = "jia", ["刚"] = "gang", ["于"] = "yu", ["连"] = "lian", ["尹"] = "yin", ["农"] = "nong",
	["匡"] = "kuang", ["钢"] = "gang", ["廷"] = "ting", ["堂"] = "tang", ["廉"] = "lian", ["本"] = "ben", ["老"] = "lao", ["霍"] =
"huo", ["欣"] = "xin", ["棠"] = "tang", ["班"] = "ban", ["虞"] = "yu", ["宗"] = "zong", ["亭"] = "ting", ["随"] = "sui", ["纲"] =
"gang", ["楚"] = "chu", ["邝"] = "kuang", ["香"] = "xiang", ["新"] = "xin", ["庭"] = "ting", ["仁"] = "ren", ["唐"] = "tang",
	["贾"] = "jia", ["崔"] = "cui", ["人"] = "ren", ["庆"] = "qing", ["南"] = "nan", ["灭"] = "mie", ["甲"] = "jia", ["余"] =
"yu", ["邬"] = "wu", ["伏"] = "fu", ["司徒"] = "situ", ["岳"] = "yue", ["琼"] = "qiong", ["民"] = "min", ["俞"] = "yu", ["钟离"] =
"zhongli", ["道"] = "dao", ["湘"] = "xiang", ["信"] = "xin", ["通"] = "tong", ["难"] = "nan", ["邹"] = "zhou", ["况"] = "kuang",
	["拓趾"] = "tuozhi", ["桐"] = "tong", ["森"] = "sen", ["秋"] = "qiu", ["任"] = "ren", ["乐"] = "yue", ["焦"] = "jiao", ["星"] =
"xing", ["遂"] = "sui", ["敏"] = "min", ["雷"] = "lei", ["高"] = "gao", ["德"] = "de", ["薛"] = "xue", ["丁"] = "ding", ["丘"] =
"qiu", ["学"] = "xue", ["祥"] = "xiang", ["翠"] = "cui", ["邱"] = "qiu", ["南宫"] = "nangong", ["同"] = "tong", ["璞"] = "pu",
	["芙"] = "fu", ["云"] = "yun", ["佟"] = "tong", ["铜"] = "tong", ["涛"] = "tao", ["明"] = "ming", ["波"] = "bo", ["孙"] =
"sun", ["滔"] = "tao", ["梁"] = "liang", ["奎"] = "kui", ["宰父"] = "zaifu", ["欧"] = "ou", ["鸣"] = "ming", ["魁"] = "kui",
	["娇"] = "jiao", ["姑"] = "gu", ["鼎"] = "ding", ["登"] = "deng", ["童"] = "tong", ["沙"] = "sha", ["雨"] = "yu", ["良"] =
"liang", ["坚"] = "jian", ["海"] = "hai", ["碧"] = "bi", ["祖"] = "zu", ["项"] = "xiang", ["定"] = "ding", ["傻"] = "sha", ["骧"] =
"xiang", ["亥"] = "hai", ["哥"] = "ge", ["归海"] = "guihai", ["统"] = "tong", ["蓉"] = "rong", ["能"] = "neng", ["行"] = "xing",
	["禹"] = "yu", ["邓"] = "deng", ["秦东"] = "qindong", ["宇"] = "yu", ["荣"] = "rong", ["东"] = "dong", ["陶"] = "tao", ["曲"] =
"qu", ["谷"] = "gu", ["韬"] = "tao", ["亮"] = "liang", ["冬"] = "dong", ["羽"] = "yu", ["隆"] = "long", ["达"] = "da", ["萧"] =
"xiao", ["屈"] = "qu", ["倪"] = "ni", ["董"] = "dong", ["玉"] = "yu", ["冷"] = "leng", ["顾"] = "gu", ["藤"] = "teng", ["晶"] =
"jing", ["敌"] = "di", ["容"] = "rong", ["韩"] = "han", ["腾"] = "teng", ["山"] = "shan", ["栋"] = "dong", ["慕容"] = "murong",
	["大"] = "da", ["狄"] = "di", ["公羊"] = "gongyang", ["黎"] = "li", ["葛"] = "ge", ["呆"] = "dai", ["澹"] = "tan", ["哉"] =
"zai", ["图"] = "tu", ["卜"] = "bo", ["辟"] = "pi", ["逆"] = "ni", ["喻"] = "yu", ["皮"] = "pi", ["左"] = "zuo", ["徒"] = "tu",
	["上官"] = "shangguan", ["雄"] = "xiong", ["炜"] = "wei", ["离"] = "li", ["戴"] = "dai", ["宰"] = "zai", ["盛"] = "sheng",
	["熊"] = "xiong", ["了"] = "liao", ["包"] = "bao", ["涂"] = "tu", ["濮"] = "pu", ["羊舌"] = "yangshe", ["晓"] = "xiao", ["珂"] =
"ke", ["斗"] = "dou", ["屠"] = "tu", ["漆雕"] = "qidiao", ["作"] = "zuo", ["胜"] = "sheng", ["髯"] = "ran", ["年"] = "nian",
	["代"] = "dai", ["布"] = "bu", ["卢"] = "lu", ["李"] = "li", ["如"] = "ru", ["廖"] = "liao", ["第"] = "di", ["里"] = "li",
	["湘云"] = "xiangyun", ["微"] = "wei", ["静"] = "jing", ["师"] = "shi", ["潘"] = "pan", ["岑"] = "cen", ["列"] = "lie", ["礼"] =
"li", ["岚"] = "lan", ["根"] = "gen", ["部"] = "bu", ["关"] = "guan", ["赞"] = "zan", ["念"] = "nian", ["莉"] = "li", ["都"] =
"du", ["韦"] = "wei", ["官"] = "guan", ["汝"] = "ru", ["梁丘"] = "liangqiu", ["楠"] = "nan", ["烈"] = "lie", ["施"] = "shi",
	["莘"] = "xin", ["倩"] = "qian", ["飘"] = "piao", ["汉"] = "han", ["湛"] = "zhan", ["商"] = "shang", ["鲁"] = "lu", ["管"] =
"guan", ["嫣然"] = "yanran", ["常"] = "chang", ["唯"] = "wei", ["琳"] = "lin", ["丽"] = "li", ["靖"] = "jing", ["阮"] = "ruan",
	["卞"] = "bian", ["赏"] = "shang", ["长"] = "chang", ["箐"] = "qing", ["才"] = "cai", ["章"] = "zhang", ["裘"] = "qiu",
	["林"] = "lin", ["为"] = "wei", ["天"] = "tian", ["瑞"] = "rui", ["财"] = "cai", ["姚"] = "yao", ["庞"] = "pang", ["聂"] =
"nie", ["上"] = "shang", ["百里"] = "baili", ["虚"] = "xu", ["添"] = "tian", ["锐"] = "rui", ["杰"] = "jie", ["发"] = "fa", ["郑"] =
"zheng", ["石"] = "shi", ["蒯"] = "kuai", ["维"] = "wei", ["贯"] = "guan", ["张"] = "zhang" }
data_name_fuxing = { ["谷梁"] = true, ["淳于"] = true, ["朝伟"] = true, ["佰赏"] = true, ["欧阳"] = true, ["伍藏"] = true, ["端木"] = true,
	["皇甫"] = true, ["令狐"] = true, ["单于"] = true, ["乐正"] = true, ["公西"] = true, ["轩辕"] = true, ["富城"] = true, ["颛孔"] = true,
	["濮阳"] = true, ["长孙"] = true, ["段千"] = true, ["微生"] = true, ["巫马"] = true, ["孟达"] = true, ["司马"] = true, ["澹台"] = true,
	["赫连"] = true, ["学友"] = true, ["德华"] = true, ["公孙"] = true, ["夏候"] = true, ["小春"] = true, ["汝鄢"] = true, ["公良"] = true,
	["曼玉"] = true, ["师空"] = true, ["南郭"] = true, ["骧驷"] = true, ["太叔"] = true, ["闻人"] = true, ["黛玉"] = true, ["楚晋"] = true,
	["涂钦"] = true, ["诸葛"] = true, ["夹谷"] = true, ["西门"] = true, ["拓哉"] = true, ["柳任"] = true, ["呼延"] = true, ["青霞"] = true,
	["东郭"] = true, ["宇文"] = true, ["宝钗"] = true, ["润发"] = true, ["东方"] = true, ["楚红"] = true, ["尉迟"] = true, ["申屠"] = true,
	["左丘"] = true, ["柏芝"] = true, ["宗政"] = true, ["星驰"] = true, ["阎法"] = true, ["公冶"] = true, ["东门"] = true, ["仲孙"] = true,
	["熙凤"] = true, ["卫健"] = true, ["司徒"] = true, ["钟离"] = true, ["拓趾"] = true, ["南宫"] = true, ["宰父"] = true, ["归海"] = true,
	["慕容"] = true, ["公羊"] = true, ["上官"] = true, ["羊舌"] = true, ["漆雕"] = true, ["湘云"] = true, ["梁丘"] = true, ["嫣然"] = true,
	["百里"] = true }
data_system_npc = {
	["官兵"] = "guan bing",
	["长乐帮帮众"] = "bang zhong",
	["贾老六"] = "jia laolu",
	["票友"] = "piao you",
	["弟子"] = "dizi",
	["厨师"] = "chu zi",
	["黑衣人"] = "heiyi ren",
	["地字门教众"] = "jiao zhong",
	["牧羊女"] = "muyang nu",
	["牧羊人"] = "muyang ren",
	["邱山风"] = "qiu shanfeng",
	["冼掌柜"] = "zhanggui",
	["兰剑"] = "lan jian",
	["殷梨亭"] = "yin liting",
	["耶律齐"] = "yelv qi",
	["黄真"] = "huang zhen",
	["青龙帮弟子"] = "dizi",
	["大盗"] = "da dao",
	["道明小师傅"] = "daoming shifu",
	["苏梦清"] = "su mengqing",
	["老头子"] = "lao touzi",
	["令狐冲"] = "linghu chong",
	["庄丁"] = "zhuang ding",
	["上官剑南"] = "shangguan jiannan",
	["方舵主"] = "fang duozhu",
	["大苦大师"] = "daku dashi",
	["文方小师太"] = "wen fang",
	["朱长龄"] = "zhu changling",
	["香客"] = "xiang ke",
	["王夫人"] = "wang furen",
	["莽汉"] = "mang han",
	["风松道长"] = "fengsong daozhang",
	["张三"] = "zhang san",
	["沈老板"] = "laoban",
	["司马大"] = "sima da",
	["小贩"] = "xiao fan",
	["乔三槐"] = "qiao",
	["维吾尔族妇女"] = "woman",
	["药店掌柜"] = "yaodian zhanggui",
	["天镜禅师"] = "tianjing chanshi",
	["成高道长"] = "chenggao daozhang",
	["蒋调侯"] = "jiang tiaohou",
	["崔莺莺"] = "cui yingying",
	["单伯山"] = "shan boshan",
	["陈运清"] = "chen yunqing",
	["万事通"] = "wanshi tong",
	["刀疤张"] = "daoba zhang",
	["小僮"] = "xiao tong",
	["范晔"] = "fan ye",
	["边防武将"] = "wu jiang",
	["乐厚"] = "le hou",
	["尼姑"] = "ni gu",
	["杨不悔"] = "yang buhui",
	["苗若兰"] = "miao ruolan",
	["李员外"] = "li yuanwai",
	["马夫子"] = "ma fuzi",
	["郝长老"] = "hao zhanglao",
	["边防官兵"] = "guan bing",
	["壮年僧人"] = "seng",
	["符敏仪"] = "fu mingyi",
	["王大米"] = "wang dami",
	["管家"] = "guan jia",
	["万老板"] = "laoban",
	["上官鹏"] = "shangguan peng",
	["成不忧"] = "cheng buyou",
	["阿根"] = "a gen",
	["胡青牛"] = "hu qingniu",
	["知客僧"] = "zhike seng",
	["裘千丈"] = "qiu qianzhang",
	["铸剑师"] = "zhujian shi",
	["流氓头"] = "liumang tou",
	["中军侍卫"] = "shiwei",
	["执法弟子"] = "zhifa dizi",
	["桑三娘"] = "sang sanniang",
	["黄老板"] = "huang laoban",
	["张巡捕"] = "zhang xunbu",
	["萧半和"] = "xiao banhe",
	["邓百川"] = "deng baichuan",
	["慧方尊者"] = "huifang zunzhe",
	["麻衣长老"] = "mayi zhanglao",
	["独行侠"] = "duxing xia",
	["于三娘"] = "yu sanniang",
	["清缘比丘"] = "biqiu",
	["无根道人"] = "wugen daoren",
	["打手"] = "da shou",
	["郑镖头"] = "zheng biaotou",
	["天字门教众"] = "jiao zhong",
	["幽草"] = "you cao",
	["小厨子"] = "xiao chuzi",
	["玄痛大师"] = "dashi",
	["波斯商人"] = "bosi shangren",
	["张镖头"] = "zhang biaotou",
	["任飞燕"] = "ren feiyan",
	["采石人"] = "caishi ren",
	["洪水旗教众"] = "jiao zhong",
	["陆柏"] = "lu bo",
	["亲兵队长"] = "qinbing duizhang",
	["温方达"] = "wen fangda",
	["卖花姑娘"] = "maihua guniang",
	["大衙役"] = "da yayi",
	["中年乞丐"] = "qi gai",
	["蒙面人"] = "mengmian ren",
	["张千"] = "zhang qian",
	["商人"] = "shang ren",
	["天门道人"] = "tianmen daoren",
	["采花子"] = "caihua zi",
	["哲罗星"] = "zheluo xing",
	["郭靖"] = "guo jing",
	["辉月使"] = "huiyue shi",
	["澄思"] = "luohan",
	["江北大盗"] = "jiangbei dadao",
	["正宫皇后"] = "huanghou",
	["小贩子"] = "xiao fan",
	["穷汉"] = "qiong han",
	["烈火旗教众"] = "jiao zhong",
	["武僧"] = "wu seng",
	["抢匪"] = "fei",
	["哑木匠"] = "ya mujiang",
	["天龙寺武僧"] = "wu seng",
	["采药人"] = "caiyao ren",
	["妇女"] = "woman",
	["狱卒"] = "yu zu",
	["许镖头"] = "xu biaotou",
	["温方山"] = "wen fangshan",
	["巡捕"] = "xun bu",
	["西域客商"] = "xiyu keshang",
	["五毒教弟子"] = "dizi",
	["瘦头陀"] = "shou toutuo",
	["石嫂"] = "shi sao",
	["蒙哥"] = "meng ge",
	["星宿派鼓手"] = "gu shou",
	["三丑"] = "san chou",
	["黎生"] = "li sheng",
	["道象禅师"] = "chanshi",
	["泉建男"] = "quan jiannan",
	["查老学士"] = "zha xueshi",
	["巡逻亲兵"] = "qing bing",
	["蓝铁匠"] = "tiejiang",
	["壮年乞丐"] = "qigai",
	["伙计"] = "huoji",
	["赵良栋"] = "zhao liangdong",
	["殷无福"] = "yin wufu",
	["慧真尊者"] = "zunzhe",
	["史伯威"] = "shi bowei",
	["胖嫂"] = "pang sao",
	["镖头"] = "biao tou",
	["紫竹"] = "zi zhu",
	["吴柏英"] = "wu baiying",
	["裘千仞"] = "qiu qianren",
	["展飞"] = "zhan fei",
	["大理武将"] = "dali wujiang",
	["张康"] = "zhang kang",
	["史镖头"] = "shi biaotou",
	["猪肉荣"] = "zhurou rong",
	["虚通"] = "xu tong",
	["老和尚"] = "monk",
	["大理官兵"] = "dali guanbing",
	["单叔山"] = "shan shushan",
	["单仲山"] = "shan zhongshan",
	["巴依"] = "bayi",
	["无相禅师"] = "chanshi",
	["跑堂"] = "waiter",
	["段正明"] = "duan zhengming",
	["私塾先生"] = "sishu xiansheng",
	["静虚师太"] = "jingxu shitai",
	["周芷若"] = "zhou zhiruo",
	["土匪"] = "fei",
	["过三拳"] = "guo sanquan",
	["丁典"] = "ding dian",
	["心清比丘"] = "xinqing biqiu",
	["元哀尊者"] = "yuanai zunzhe",
	["钟镇"] = "zhong zhen",
	["土娼"] = "tu chang",
	["凤天南"] = "feng tiannan",
	["崔算盘"] = "cui suanpan",
	["单小山"] = "shan xiaoshan",
	["达尔巴"] = "daer ba",
	["慕容复"] = "murong fu",
	["黑龙门弟子"] = "dizi",
	["木匠"] = "mu jiang",
	["妙风使"] = "miaofeng shi",
	["清乐比丘"] = "biqiu",
	["张淡月"] = "zhang danyue",
	["龟奴"] = "gui nu",
	["丫头"] = "ya tou",
	["进香客"] = "jinxiang ke",
	["范长老"] = "fan zhanglao",
	["胡斐"] = "hu fei",
	["游驹"] = "you ju",
	["菊剑"] = "ju jian",
	["大理小贩"] = "dali xiaofan",
	["虚明"] = "xu ming",
	["徐霞客"] = "xu xiake",
	["逍遥子"] = "xiaoyao zi",
	["梁发"] = "liang fa",
	["元广波"] = "yuan guangbo",
	["天松道人"] = "tiansong daoren",
	["殷无寿"] = "yin wushou",
	["渡难"] = "du nan",
	["值勤兵"] = "zhiqin bing",
	["程玉环"] = "cheng yuhuan",
	["陈达海"] = "chen dahai",
	["护法使者"] = "shi zhe",
	["木人"] = "mu ren",
	["狄修"] = "di xiu",
	["澄意"] = "luohan",
	["万青里"] = "wan qingli",
	["书生"] = "shu sheng",
	["傅思归"] = "fu sigui",
	["丐帮弟子"] = "gaibang dizi",
	["玉玑子"] = "yuji zi",
	["船老大"] = "lao da",
	["米横野"] = "mi hengye",
	["菊友"] = "ju you",
	["空空儿"] = "kong kong",
	["唢呐手"] = "suona shou",
	["农夫"] = "fu",
	["梅超风"] = "mei chaofeng",
	["温方施"] = "wen fangshi",
	["钱善人"] = "qian shanren",
	["殷锦"] = "yin jin",
	["钱缝"] = "qian feng",
	["何师我"] = "he shiwo",
	["冯铁匠"] = "feng tiejiang",
	["回族兵"] = "huizu bing",
	["马舵主"] = "ma duozhu",
	["胜谛"] = "sheng di",
	["镖师"] = "biao shi",
	["红娘"] = "hong niang",
	["古笃诚"] = "gu ducheng",
	["黄伯流"] = "huang boliu",
	["地痞"] = "dipi",
	["江来福"] = "jiang laifu",
	["货郎"] = "huo lang",
	["周虎威"] = "zhou huwei",
	["青鬏龟童"] = "gui tong",
	["侯通海"] = "hou tonghai",
	["方评"] = "fang ping",
	["哈萨克牧民"] = "hasake mumin",
	["任盈盈"] = "ren yingying",
	["公冶乾"] = "gongye gan",
	["本观大师"] = "benguan dashi",
	["洪安通"] = "hong antong",
	["冼老板"] = "xian laoban",
	["俞朝奉"] = "yu chaofeng",
	["大汉"] = "da han",
	["苗族青年"] = "miaozu qingnian",
	["阎基"] = "yan ji",
	["汤英鹗"] = "tang yinge",
	["玉磬子"] = "yuqing zi",
	["程瑶迦"] = "cheng yaojia",
	["陈长老"] = "chen zhanglao",
	["瑛姑"] = "ying gu",
	["彭连虎"] = "peng lianhu",
	["哲布尊巴丹"] = "huo fo",
	["郁光标"] = "yu guangbiao",
	["宋老板"] = "laoban",
	["粘而帖"] = "zhan ertie",
	["绿衣弟子"] = "dizi",
	["顾炎武"] = "gu yanwu",
	["静和师太"] = "jinghe shitai",
	["陈正德"] = "chen zhengde",
	["乌老大"] = "wu laoda",
	["沙天江"] = "sha tianjiang",
	["苏鲁克"] = "su luke",
	["黄龙门弟子"] = "dizi",
	["陆高轩"] = "lu gaoxuan",
	["完颜萍"] = "wanyan ping",
	["吴劲草"] = "wu jincao",
	["邓八公"] = "deng bagong",
	["知府大人"] = "zhifu daren",
	["流氓"] = "liu mang",
	["赵敏"] = "zhao min",
	["朱九真"] = "zhu jiuzhen",
	["徐小五"] = "xu xiaowu",
	["张中"] = "zhang zhong",
	["金人"] = "jin ren",
	["史仲猛"] = "shi zhongmeng",
	["客商"] = "ke shang",
	["衙役"] = "ya yi",
	["大理镖头"] = "biaotou",
	["静照师太"] = "jingzhao shitai",
	["马青雄"] = "ma qingxiong",
	["陈清"] = "chen qing",
	["蒙古卫士"] = "weishi",
	["老仆"] = "laopu",
	["小道童"] = "daotong",
	["蓝凤凰"] = "lan fenghuang",
	["黑衣杀手"] = "shashou",
	["执戒僧"] = "zhijie seng",
	["烧饼刘"] = "shaobing liu",
	["澄寂"] = "luohan",
	["老者"] = "lao zhe",
	["绿衣小僮"] = "luyi xiaotong",
	["周孤桐"] = "zhou gutong",
	["苗人凤"] = "miao renfeng",
	["小侍童"] = "xiao shitong",
	["侍粥僧"] = "shizhou seng",
	["枯荣长老"] = "kurong zhanglao",
	["苏习之"] = "su xizhi",
	["店小二"] = "xiao er",
	["刘老实"] = "liu laoshi",
	["扫地仆人"] = "saodi puren",
	["静迦师太"] = "jingjia shitai",
	["计老人"] = "ji laoren",
	["张妈"] = "zhang ma",
	["厚土旗教众"] = "jiao zhong",
	["玄渡大师"] = "dashi",
	["家丁"] = "jia ding",
	["小乞丐"] = "xiao qigai",
	["香女"] = "xiang nu",
	["进喜儿"] = "jinxi er",
	["静玄师太"] = "jingxuan shitai",
	["王老汉"] = "wang laohan",
	["小二"] = "xiao er",
	["仪清"] = "yi qing",
	["穆人清"] = "mu renqing",
	["丫鬟"] = "ya huan",
	["黑衣大盗"] = "heiyi dadao",
	["说不得"] = "shuo bude",
	["慧空尊者"] = "zunzhe",
	["赌客"] = "du ke",
	["朱老板"] = "laoban",
	["仆人"] = "pu ren",
	["樵夫"] = "qiao fu",
	["本参大师"] = "bencan dashi",
	["蒋涛"] = "jiang tao",
	["黛绮丝"] = "dai qisi",
	["周颠"] = "zhou dian",
	["道品禅师"] = "chanshi",
	["谷虚道长"] = "guxu daozhang",
	["鸠摩智"] = "jiumo zhi",
	["赵城之"] = "zhao chengzhi",
	["樵子"] = "qiao zi",
	["方碧琳"] = "fang bilin",
	["了思禅师"] = "liaosi chanshi",
	["小喽罗"] = "xiao louluo",
	["肖老头"] = "xiao laotou",
	["江湖豪客"] = "hao ke",
	["波斯生意人"] = "shengyi ren",
	["王长老"] = "wang zhanglao",
	["昆仑派弟子"] = "dizi",
	["凌退思"] = "ling tuisi",
	["天柏道人"] = "tianbai daoren",
	["玄惭大师"] = "dashi",
	["许雪亭"] = "xu xueting",
	["丁敏君"] = "ding minjun",
	["何红药"] = "he hongyao",
	["苗族老汉"] = "miaozu laohan",
	["执法僧兵"] = "bing",
	["殷老板"] = "yin laoban",
	["余婆婆"] = "yu popo",
	["温仪"] = "wen yi",
	["清晓比丘"] = "biqiu",
	["归二娘"] = "gui erniang",
	["钱眼开"] = "qian yankai",
	["王公公"] = "wang gonggong",
	["生意人"] = "shengyi ren",
	["吴青烈"] = "wu qinglie",
	["风清扬"] = "feng qingyang",
	["夏雪宜"] = "xia xueyi",
	["采矿师傅"] = "caikuang shifu",
	["珠宝商"] = "zhubao shang",
	["秦绢"] = "qin juan",
	["药师"] = "yao shi",
	["归钟"] = "gui zhong",
	["都大锦"] = "du dajin",
	["费彬"] = "fei bin",
	["李招财"] = "li zhaocai",
	["清善比丘"] = "biqiu",
	["张泉"] = "zhang quan",
	["火工头陀"] = "huogong toutuo",
	["独行大侠"] = "duxing daxia",
	["牛老板"] = "niu laoban",
	["京城客"] = "jingcheng ke",
	["杂货铺老板"] = "laoban",
	["游客"] = "you ke",
	["葛光佩"] = "ge guangpei",
	["大理地痞"] = "dipi",
	["公孙止"] = "gongsun zhi",
	["王合计"] = "wang heji",
	["丁婆婆"] = "ding popo",
	["武敦儒"] = "wu dunru",
	["星宿派号手"] = "hao shou",
	["奚长老"] = "xi zhanglao",
	["天虹禅师"] = "tianhong chanshi",
	["元决尊者"] = "yuanjue zunzhe",
	["史孟捷"] = "shi mengjie",
	["铜人"] = "tong ren",
	["巴天石"] = "ba tianshi",
	["东方不败"] = "dongfang bubai",
	["宁中则"] = "ning zhongze",
	["何铁手"] = "he tieshou",
	["王三力"] = "wang sanli",
	["元悔尊者"] = "yuanhui zunzhe",
	["卫天望"] = "wei tianwang",
	["林震南"] = "lin zhennan",
	["绿竹翁"] = "lu zhuweng",
	["西方月"] = "fae shizhe",
	["宋远桥"] = "song yuanqiao",
	["酒楼掌柜"] = "jiulou zhanggui",
	["账房先生"] = "zhangfang xiansheng",
	["盐枭"] = "yanxiao",
	["过彦之"] = "guo yanzhi",
	["文音小师太"] = "wenyin",
	["男孩"] = "boy",
	["桃花姑娘"] = "tao hua",
	["冯默风"] = "feng mofeng",
	["孙婆婆"] = "sun popo",
	["清观比丘"] = "biqiu",
	["药铺老板"] = "laoban",
	["侍茶小僮"] = "tong",
	["枯荣禅师"] = "kurong chanshi",
	["赤龙门弟子"] = "dizi",
	["莫声谷"] = "mo shenggu",
	["高升泰"] = "gao shengtai",
	["静真师太"] = "jingzhen shitai",
	["陈阿婆"] = "chen apo",
	["武修文"] = "wu xiuwen",
	["全冠清"] = "quan guanqing",
	["中年男子"] = "zhongnian nanzi",
	["裸体女子"] = "luoti nvzi",
	["韦春芳"] = "wei chunfang",
	["辛双清"] = "xin shuangqing",
	["黄大雄"] = "huang daxiong",
	["无名老翁"] = "lao weng",
	["纺织女"] = "fangzhi nu",
	["学童"] = "xue tong",
	["盐商"] = "yanshang",
	["一品堂 武士"] = "wu shi",
	["定逸师太"] = "dingyi shitai",
	["鲍大楚"] = "bao dachu",
	["红衣杀手"] = "shashou",
	["从不弃"] = "cong buqi",
	["澄灭"] = "luohan",
	["清为比丘"] = "biqiu",
	["茶馆小二"] = "chaguan xiaoer",
	["岭南大侠"] = "daxia",
	["大理衙役"] = "dali yayi",
	["四丑"] = "si chou",
	["蒋舵主"] = "jiang duozhu",
	["澄心"] = "luohan",
	["渡劫"] = "du jie",
	["文清小师太"] = "wen qing",
	["杏芳"] = "xing fang",
	["王语嫣"] = "wang yuyan",
	["崔老板"] = "cui laoban",
	["吴长老"] = "wu zhanglao",
	["澄信"] = "luohan",
	["道一禅师"] = "chanshi",
	["马钰"] = "ma yu",
	["裘千尺"] = "qiu qianchi",
	["刀客"] = "dao ke",
	["色楞"] = "se leng",
	["何太冲"] = "he taichong",
	["何足道"] = "he zudao",
	["酒保"] = "jiu bao",
	["董帐房"] = "zhangfang",
	["马夫"] = "ma fu",
	["齐云敖"] = "qi yunao",
	["玉灵子"] = "yuling zi",
	["知客道长"] = "zhike daozhang",
	["张算盘"] = "zhang suanpan",
	["五丑"] = "wu chou",
	["元悲尊者"] = "yuanbei zunzhe",
	["李捕头"] = "li butou",
	["大理赌客"] = "duke",
	["邝宝官"] = "kuang baoguan",
	["接引弟子"] = "jieyin dizi",
	["缘根和尚"] = "yuangen",
	["傻姑"] = "sha gu",
	["道尘禅师"] = "chanshi",
	["唐文德"] = "tang wende",
	["玉虚散人"] = "yuxu sanren",
	["昆仑派女弟子"] = "dizi",
	["程青霜"] = "cheng qingshuang",
	["采花大盗"] = "caihua dadao",
	["韩铁匠"] = "han tiejiang",
	["澄明"] = "luohan",
	["清法比丘"] = "biqiu",
	["当铺老板"] = "lao ban",
	["江湖艺人"] = "yiren",
	["打铁匠"] = "jiang",
	["王城"] = "wang cheng",
	["杨莲亭"] = "yang lianting",
	["星宿派钹手"] = "bo shou",
	["赵爵爷"] = "zhao jueye",
	["建除道人"] = "jianchu daoren",
	["尹克西"] = "yin kexi",
	["道果禅师"] = "chanshi",
	["卜沉"] = "bu chen",
	["史叔刚"] = "shi shugang",
	["茶博士"] = "cha boshi",
	["林平之"] = "lin pingzhi",
	["来福儿"] = "laifu er",
	["矮老者"] = "ai laozhe",
	["劳德诺"] = "lao denuo",
	["孙老板"] = "sun laoban",
	["澄坚"] = "luohan",
	["李式开"] = "li shikai",
	["茅十八"] = "mao shiba",
	["一灯大师"] = "yideng dashi",
	["傣族大嫂"] = "daizu dasao",
	["萧中慧"] = "xiao zhonghui",
	["梁子翁"] = "liang ziweng",
	["风波恶"] = "feng boe",
	["藏族牧民"] = "zangzu mumin",
	["宋长老"] = "song zhanglao",
	["黄眉和尚"] = "huangmei heshang",
	["贾布"] = "jia bu",
	["史登达"] = "shi dengda",
	["摘星子"] = "zhaixing zi",
	["胡贵"] = "hu gui",
	["养蚕婆婆"] = "yangcan popo",
	["趟子手"] = "tangzi shou",
	["雪仙儿"] = "xue xianer",
	["李半仙"] = "li banxian",
	["落魄公子"] = "gong zi",
	["祖千秋"] = "zu qianqiu",
	["岳灵珊"] = "yue lingshan",
	["陆无双"] = "lu wushuang",
	["西夏兵"] = "xixia bing",
	["慕容博"] = "murong bo",
	["黄蓉"] = "huang rong",
	["炼丹道童"] = "daotong",
	["小土匪"] = "xiao tufei",
	["护镖镖师"] = "hubiao biaoshi",
	["飘然子"] = "piaoran zi",
	["成昆"] = "cheng kun",
	["大癫大师"] = "dadian dashi",
	["澄行"] = "luohan",
	["陈近南"] = "chen jinnan",
	["洪哮天"] = "hong xiaotian",
	["采药道长"] = "caiyao daozhang",
	["范遥"] = "fan yao",
	["老乞丐"] = "qigai",
	["岳不群"] = "yue buqun",
	["打铁僧"] = "datie seng",
	["温南扬"] = "wen nanyang",
	["刘竹庄"] = "liu zhuzhuang",
	["大理盐枭"] = "yanxiao",
	["看窑弟子"] = "kanyao dizi",
	["定闲师太"] = "dingxian shitai",
	["上官云"] = "shangguan yun",
	["龙卷风"] = "long juanfeng",
	["守律僧"] = "seng",
	["白世镜"] = "bai shijing",
	["双儿"] = "shuang er",
	["蒙古武士"] = "wushi",
	["容子矩"] = "rong ziju",
	["严掌柜"] = "zhanggui",
	["曲洋"] = "qu yang",
	["无名小子"] = "wuming xiaozi",
	["青龙门弟子"] = "dizi",
	["白龙门弟子"] = "dizi",
	["掌柜"] = "zhanggui",
	["颜垣"] = "yan tan",
	["道成禅师"] = "chanshi",
	["慧色尊者"] = "zunzhe",
	["采花贼"] = "caihua zei",
	["鹤笔翁"] = "he biweng",
	["小沙弥"] = "shami",
	["茶客"] = "cha ke",
	["玄澄大师"] = "dashi",
	["金轮法王"] = "jinlun fawang",
	["萧老板"] = "xiao laoban",
	["元兵"] = "yuan bing",
	["孤鸿子"] = "guhong zi",
	["文菲小师太"] = "wen fei",
	["袁冠男"] = "yuan guannan",
	["亲兵"] = "qin bing",
	["陆冠英"] = "lu guanying",
	["竹剑"] = "zhu jian",
	["老裁缝"] = "lao caifeng",
	["钟志灵"] = "zhong zhiling",
	["张松溪"] = "zhang songxi",
	["大理盐商"] = "yanshang",
	["玄生大师"] = "dashi",
	["挑夫"] = "tiao fu",
	["落魄书生"] = "luopo shusheng",
	["乔福"] = "qiao fu",
	["公孙绿萼"] = "gongsun lve",
	["万大平"] = "wan daping",
	["小翠"] = "xiao cui",
	["天狼子"] = "tianlang zi",
	["回族武将"] = "wu jiang",
	["星宿派弟子"] = "dizi",
	["游坦之"] = "you tanzhi",
	["朱丹臣"] = "zhu danchen",
	["哈萨克妇女"] = "woman",
	["花铁干"] = "hua tiegan",
	["桑结"] = "sang jie",
	["王坏水"] = "wang huaishui",
	["钟万仇"] = "zhong wanchou",
	["静空师太"] = "jingkong shitai",
	["慧云尊者"] = "huiyun zunzhe",
	["元怒尊者"] = "yuannu zunzhe",
	["捕头"] = "bu tou",
	["武三通"] = "wu santong",
	["风字门教众"] = "jiao zhong",
	["黄药师"] = "huang yaoshi",
	["了宽禅师"] = "liaokuan chanshi",
	["了清禅师"] = "liaoqing chanshi",
	["忽必烈"] = "hu bilie",
	["洪七公"] = "hong qigong",
	["静慧师太"] = "jinghui shitai",
	["小童"] = "xiao tong",
	["小孩"] = "kid",
	["总坛弟子"] = "dizi",
	["常遇春"] = "chang yuchun",
	["闻苍松"] = "wen cangsong",
	["皇宫侍卫"] = "shiwei",
	["豆腐西施"] = "doufu xishi",
	["女弟子"] = "dizi",
	["张朝唐"] = "zhang chaotang",
	["钟小二"] = "zhong xiaoer",
	["魔教杀手"] = "mojiao shashou",
	["方生大师"] = "fangsheng dashi",
	["了惑禅师"] = "liaohuo chanshi",
	["瑞婆婆"] = "rui popo",
	["侯君集"] = "hou junji",
	["张帐房"] = "zhangfang",
	["男弟子"] = "dizi",
	["纪晓芙"] = "ji xiaofu",
	["仪琳"] = "yi lin",
	["俞岱岩"] = "yu daiyan",
	["迟百城"] = "chi baicheng",
	["富家公子"] = "gong zi",
	["澄欲"] = "luohan",
	["慧名尊者"] = "zunzhe",
	["清闻比丘"] = "biqiu",
	["阿曼"] = "aman",
	["灰衣帮众"] = "bangzhong",
	["李春来"] = "li chunlai",
	["陈冲之"] = "chen chongzhi",
	["童百熊"] = "tong baixiong",
	["锐金旗教众"] = "jiao zhong",
	["苏荃"] = "su quan",
	["波罗星"] = "boluo xing",
	["彭莹玉"] = "peng yingyu",
	["俞莲舟"] = "yu lianzhou",
	["赵灵珠"] = "zhao lingzhu",
	["李老板"] = "li laoban",
	["东方日"] = "shangshan shizhe",
	["赌场老板"] = "laoban",
	["血刀老祖"] = "xuedao laozu",
	["哲别"] = "zhe bie",
	["盐枭老者"] = "yanxiao",
	["苗兵"] = "miao bing",
	["高克新"] = "gao kexin",
	["一品堂武士"] = "wu shi",
	["归辛树"] = "gui xinshu",
	["阿朱"] = "a zhu",
	["严妈妈"] = "yan mama",
	["西华子"] = "xi huazi",
	["辛然"] = "xin ran",
	["王五"] = "wang wu",
	["巫师"] = "wizard",
	["静风师太"] = "jingfeng shitai",
	["扫地僧"] = "saodi seng",
	["厨子"] = "chu zi",
	["游方和尚"] = "seng ren",
	["独脚大盗"] = "dujiao dadao",
	["马光佐"] = "ma guangzuo",
	["詹春"] = "zhan chun",
	["澄观"] = "luohan",
	["摩诃巴思"] = "mohe basi",
	["侍剑"] = "shi jian",
	["铁匠"] = "tie jiang",
	["静闲师太"] = "jingxian shitai",
	["郑萼"] = "zheng e",
	["张夫人"] = "zhang furen",
	["田伯光"] = "tian boguang",
	["小丫鬟"] = "xiao yahuan",
	["总管家"] = "zong guanjia",
	["护林僧兵"] = "bing",
	["白衣武士"] = "wei shi",
	["青衣武士"] = "wei shi",
	["李铁嘴"] = "li tiezui",
	["江湖浪子"] = "jianghu langzi",
	["买卖提"] = "maimaiti",
	["轿夫头"] = "jiaofu tou",
	["黄富霸"] = "huang fuba",
	["任无月"] = "Ren wuyue",
	["包子王"] = "baozi wang",
	["阿紫"] = "a zi",
	["朱元璋"] = "zhu yuanzhang",
	["庄铮"] = "zhuang zheng",
	["药铺伙计"] = "huoji",
	["吴光胜"] = "wu guangsheng",
	["平婆婆"] = "ping popo",
	["方天劳"] = "fang tianlao",
	["宝象"] = "bao xiang",
	["铁人"] = "tie ren",
	["钱青健"] = "qian qingjian",
	["单季山"] = "shan jishan",
	["梅剑"] = "mei jian",
	["了明禅师"] = "liaoming chanshi",
	["黯然子"] = "anran zi",
	["鲁有脚"] = "lu youjiao",
	["元痛尊者"] = "yuantong zunzhe",
	["澄净"] = "luohan",
	["澄尚"] = "luohan",
	["贝海石"] = "bei haishi",
	["苏普"] = "su pu",
	["麦鲸"] = "mai jing",
	["赵知仁"] = "zhao zhiren",
	["陆乘风"] = "lu chengfeng",
	["翠花"] = "cui hua",
	["温方义"] = "wen fangyi",
	["丁春秋"] = "ding chunqiu",
	["道正禅师"] = "chanshi",
	["潘天耕"] = "pan tiangeng",
	["公子哥儿"] = "gong zi",
	["岭南大盗"] = "dadao",
	["班淑娴"] = "ban shuxian",
	["道觉禅师"] = "chanshi",
	["丁当"] = "ding dang",
	["李莫愁"] = "li mochou",
	["丁勉"] = "ding mian",
	["薛慕华"] = "xue muhua",
	["方正大师"] = "da shi",
	["酒馆老板"] = "jiuguan laoban",
	["平一指"] = "ping yizhi",
	["张老板"] = "laoban",
	["陆立鼎"] = "lu liding",
	["五毒教女弟子"] = "dizi",
	["游骥"] = "you ji",
	["武馆门卫"] = "men wei",
	["锣鼓手"] = "luogu shou",
	["女店主"] = "nv dianzhu",
	["萧峰"] = "xiao feng",
	["慧修尊者"] = "zunzhe",
	["胖头陀"] = "pang toutuo",
	["薛烛"] = "xue zhu",
	["小龙女"] = "xiao longnv",
	["徐达"] = "xu da",
	["嫖客"] = "piao ke",
	["仪和"] = "yi he",
	["了行禅师"] = "liaoxing chanshi",
	["总镖头"] = "zong biaotou",
	["作坊主人"] = "zuofang zhuren",
	["慧如尊者"] = "zunzhe",
	["文晖小师太"] = "wen hui",
	["绿衣僮儿"] = "luyi tonger",
	["计无施"] = "ji wushi",
	["陈玄风"] = "chen xuanfeng",
	["玄慈大师"] = "dashi",
	["龙三爷"] = "long sanye",
	["桃花"] = "tao hua",
	["牧童"] = "mutong",
	["温卧儿"] = "wenwo er",
	["梁长老"] = "liang zhanglao",
	["澄识"] = "luohan",
	["澄知"] = "luohan",
	["史季强"] = "shi jiqiang",
	["浪荡公子"] = "gong zi",
	["盐商头子"] = "yanshang",
	["高根明"] = "gao genming",
	["水兵"] = "shui bing",
	["飞天子"] = "feitian zi",
	["凌霜华"] = "ling shuanghua",
	["章达夫"] = "zhang dafu",
	["者勒米"] = "zhe lemi",
	["项长老"] = "xiang zhanglao",
	["王潭"] = "wang tan",
	["鹿杖客"] = "lu zhangke",
	["阿香"] = "a xiang",
	["萨老板"] = "laoban",
	["老板"] = "lao ban",
	["温方悟"] = "wen fangwu",
	["出尘子"] = "chuchen zi",
	["玄苦大师"] = "dashi",
	["善勇"] = "shan yong",
	["欧阳老板"] = "ouyang laoban",
	["仇松年"] = "chou songnian",
	["雷字门教众"] = "jiao zhong",
	["烧饭僧"] = "shaofan seng",
	["关明梅"] = "guan mingmei",
	["玄悲大师"] = "dashi",
	["说书人"] = "shuoshu ren",
	["崔员外"] = "cui yuanwai",
	["李裁缝"] = "li caifeng",
	["华赫艮"] = "hua hegen",
	["花剑影"] = "Hua jianying",
	["凌震天"] = "ling zhentian",
	["狮吼子"] = "shihou zi",
	["尼摩星"] = "nimo xing",
	["大痴大师"] = "dachi dashi",
	["玄寂大师"] = "dashi",
	["王掌柜"] = "zhanggui",
	["周伯通"] = "zhou botong",
	["渡厄"] = "du e",
	["段正淳"] = "duan zhengchun",
	["盖一鸣"] = "gai yiming",
	["施戴子"] = "shi daizi",
	["黄令天"] = "huang lingtian",
	["澄和"] = "luohan",
	["清无比丘"] = "biqiu",
	["青年乞丐"] = "qigai",
	["沙通天"] = "sha tongtian",
	["月下老人"] = "yuexia laoren",
	["霍先生"] = "xianshen",
	["封不平"] = "feng buping",
	["陆大有"] = "lu dayou",
	["巨木旗教众"] = "jiao zhong",
	["船夫"] = "chuan fu",
	["侍女"] = "shi nv",
	["老大娘"] = "lao daniang",
	["殷野王"] = "yin yewang",
	["庙祝"] = "miao zhu",
	["钟阿四"] = "zhong asi",
	["钟四嫂"] = "zhong sisao",
	["游迅"] = "you xun",
	["僧人"] = "seng ren",
	["阿祥"] = "a xiang",
	["官府士兵"] = "guanfu shibing",
	["本因大师"] = "benyin dashi",
	["慧虚尊者"] = "zunzhe",
	["司空玄"] = "sikong xuan",
	["静心师太"] = "jingxin shitai",
	["凤七"] = "feng qi",
	["仪文"] = "yi wen",
	["常长风"] = "chang changfeng",
	["郭芙"] = "guo fu",
	["吕文德"] = "lv wende",
	["张无忌"] = "zhang wuji",
	["澄灵"] = "luohan",
	["渔人"] = "yu ren",
	["静道师太"] = "jingdao shitai",
	["樊一翁"] = "fan yiweng",
	["剑客"] = "jian ke",
	["程灵素"] = "cheng lingsu",
	["单正"] = "shan zheng",
	["卓不凡"] = "zhuo bufan",
	["阿拉木罕"] = "alamuhan",
	["慧光尊者"] = "zunzhe",
	["侯三姐"] = "hou sanjie",
	["赵老板"] = "laoban",
	["哑妇"] = "ya fu",
	["公子哥"] = "gongzi ge",
	["黑林钵夫"] = "heilin bofu",
	["大丑"] = "da chou",
	["巡捕王"] = "xunbu wang",
	["元伤尊者"] = "yuanshang zunzhe",
	["托钵僧"] = "tuobo seng",
	["平威"] = "ping wei",
	["史青山"] = "shi qingshan",
	["神秘镖师"] = "shenmi biaoshi",
	["了修禅师"] = "liaoxiu chanshi",
	["殷无禄"] = "yin wulu",
	["慧洁尊者"] = "zunzhe",
	["女孩"] = "girl",
	["凤一鸣"] = "feng yiming",
	["潇湘子"] = "xiaoxiang zi",
	["沈青刚"] = "shen qinggang",
	["阿碧"] = "a bi",
	["守寺僧兵"] = "bing",
	["宗赞王子"] = "zongzan wangzi",
	["藏族妇女"] = "woman",
	["殷天正"] = "yin tianzheng",
	["戏子"] = "xi zi",
	["吴大鹏"] = "wu dapeng",
	["甘宝宝"] = "gan baobao",
	["贝锦仪"] = "bei jinyi",
	["风陵师太"] = "fengling shitai",
	["灭绝师太"] = "miejue shitai",
	["丁同"] = "ding tong",
	["殷离"] = "yin li",
	["无名老僧"] = "wuming laoseng",
	["巫婆"] = "wu po",
	["小和尚"] = "monk",
	["褚万里"] = "zhu wanli",
	["沈铁匠"] = "shen tiejiang",
	["向问天"] = "xiang wentian",
	["艄公"] = "shao gong",
	["师爷"] = "shi ye",
	["道童"] = "dao tong",
	["呼巴音"] = "hu bayin",
	["平四"] = "ping si",
	["小丫环"] = "xiao yahuan",
	["周威信"] = "zhou",
	["侍童"] = "shi tong",
	["包不同"] = "bao butong",
	["左冷禅"] = "zuo lengchan",
	["张浩天"] = "zhang haotian",
	["蒙面大盗"] = "mengmian dadao",
	["灵智上人"] = "lingzhi shangren",
	["钱老本"] = "qian laoben",
	["钟灵"] = "zhong ling",
	["李明霞"] = "li mingxia",
	["西夏武士"] = "wu shi",
	["李文秀"] = "li wenxiu",
	["谢逊"] = "xie xun",
	["宋青书"] = "song qingshu",
	["裸体男子"] = "luoti nanzi",
	["轿夫"] = "jiao fu",
	["华辉"] = "hua hui",
	["哑巴"] = "yaba",
	["达官贵人"] = "gui ren",
	["行者"] = "xing zhe",
	["玉音子"] = "yuyin zi",
	["穆念慈"] = "mu nianci",
	["土匪头"] = "tufei tou",
	["李二嫂"] = "li ersao",
	["马掌柜"] = "ma zhanggui",
	["简长老"] = "jian zhanglao",
	["杨逍"] = "yang xiao",
	["庄家"] = "zhuang jia",
	["刘好弈"] = "liu haoyi",
	["史松"] = "shi song",
	["龚光杰"] = "gong guangjie",
	["淳于蓝"] = "chunyu lan",
	["定静师太"] = "dingjing shitai",
	["苗家女子"] = "miaojia nuzi",
	["门主"] = "men zhu",
	["红衣武士"] = "wei shi",
	["朱富"] = "zhu fu",
	["本相大师"] = "benxiang dashi",
	["看守"] = "kan shou",
	["杨过"] = "yang guo",
	["流云使"] = "liuyun shi",
	["韦一笑"] = "wei yixiao",
	["澄志"] = "luohan",
	["干光豪"] = "gan guanghao",
	["彝族小伙"] = "yizu xiaohuo",
	["白袍剑侠"] = "baipao jianxia",
	["林玉龙"] = "lin yulong",
	["教书先生"] = "xian sheng",
	["冷谦"] = "leng qian",
	["唐洋"] = "tang yang",
	["李公公"] = "li gonggong",
	["阿庆嫂"] = "aqing sao",
	["婢女"] = "bi nu",
	["马贼"] = "ma zei",
	["王厨子"] = "wang chuzi",
	["黑衣帮众"] = "bangzhong",
	["张三丰"] = "zhang sanfeng",
	["唐光雄"] = "tang guangxiong",
	["施琅"] = "shi lang",
	["道相禅师"] = "chanshi",
	["老秀才"] = "lao xiucai",
	["马五德"] = "ma wude",
	["何师爷"] = "he shiye",
	["仪质"] = "yi zhi",
	["高则成"] = "gao zecheng",
	["武将"] = "wu jiang",
	["长安客商"] = "changan keshang",
	["左子穆"] = "zuo zimu",
	["刘老板"] = "laoban",
	["高老者"] = "gao laozhe",
	["缘根"] = "yuan gen",
	["二丑"] = "er chou",
	["慧合尊者"] = "zunzhe",
	["玄难大师"] = "dashi",
	["猴子"] = "hou zi",
	["青蛙"] = "qing wa",
	["毒蛇"] = "du she",
	["杨康"] = "yang kang",
	["赵志敬"] = "zhao zhijing",
	["掌药道长"] = "zhangyao daozhang",
	["掌经道长"] = "zhangjing daozhang",
	["韩小莹"] = "han xiaoying",
	["华筝公主"] = "Huazheng",
	["柯镇恶"] = "ke zhene",
	["苟读"] = "gou du",
	["老虎"] = "lao hu",
	["虚竹"] = "xu zhu",
	["谭处端"] = "tan chuduan",
	["李志常"] = "li zhichang",
	["丘处机"] = "qiu chuji",
	["韩宝驹"] = "han baoju",
	["全金发"] = "quan jinfa",
	["艺人"] = "yi ren",
	["平寇将军"] = "pingkou jiangjun",
	["狗仆"] = "gou pu",
	["车骑将军"] = "cheqi jiangjun",
	["折冲将军"] = "zhechong jiangjun",
	["征东将军"] = "zhengdong jiangjun",
	["简捷"] = "jian jie",
	["杀手的雕像"] = "statuary",
	["闪电的雕像"] = "statuary",
	["近卫兵"] = "jinwei bing",
	["冯阿三"] = "feng asan",
	["大理老者"] = "yanxiao",
	["王处一"] = "wang chuyi",
	["掌园道长"] = "zhangyuan daozhang",
	["刘处玄"] = "liu chuxuan",
	["术赤"] = "shu chi",
	["康广陵"] = "kang guangling",
	["苏星河"] = "su xinghe",
	["拖雷"] = "tuo lei",
	["铁木真"] = "tiemuzhen",
	["近卫兵"] = "jinwei bing",
	["獒犬"] = "dog",
	["察合台"] = "cha hetai",
	["朱聪"] = "zhu cong",
	["火工道人"] = "huogong daoren",
	["祁志诚"] = "qi zhicheng",
	["崔志方"] = "cui zhifang",
	["掌理道长"] = "zhangli daozhang",
	["郝大通"] = "hao datong",
	["申志凡"] = "shen zhifan",
	["许老板"] = "xu laoban",
	["尹志平"] = "yin zhiping",
	["孙不二"] = "sun buer",
	["王志坦"] = "wang zhitan",
	["李萍"] = "li ping",
	["郭啸天"] = "guo xiaotian",
	["野猪"] = "ye zhu",
	["神雕"] = "shen diao",
	["蔡德忠"] = "cai dezhong",
	["房志起"] = "fang zhiqi",
	["南希仁"] = "nan xiren",
	["张阿生"] = "zhang asheng",
	["博尔术"] = "boer shu",
	["蜜蜂"] = "bee",
}
--新手安全房间从地图copy生成
var["newbie_rooms"] =
"51|52|53|54|55|56|57|58|59|60|61|62|63|64|65|66|67|68|69|70|71|72|73|74|75|76|77|78|79|80|81|82|83|84|85|86|87|88|89|90|91|92|93|94|95|96|97|98|99|100|101|102|103|104|105|106|107|108|109|110|111|112|113|114|115|116|117|118|119|120|121|122|123|124|125|126|127|128|129|130|131|132|133|134|135|136|137|138|139|140|141|142|143|144|145|146|147|148|149|150|151|154|155|156|157|158|159|160|161|162|163|164|165|166|167|168|169|170|171|172|173|174|175|176|177|178|179|180|181|182|183|184|185|186|187|188|189|190|191|192|193|194|195|196|197|198|199|200|201|202|203|204|205|206|207|208|210|211|212|213|214|215|216|218|219|220|221|222|223|224|225|226|227|228|229|230|231|232|233|234|235|236|237|238|239|240|241|242|243|244|245|246|247|248|249|250|251|252|253|254|255|256|257|258|259|260|261|262|263|264|265|266|267|268|269|270|271|272|273|274|275|276|277|278|279|280|281|282|283|284|285|286|287|288|289|291|292|293|294|295|296|297|298|299|300|301|302|303|304|305|306|307|308|309|310|311|312|313|314|315|316|317|318|319|320|321|322|323|324|325|326|327|328|329|330|331|332|333|334|335|336|337|338|339|340|341|342|343|344|345|346|347|348|349|350|351|352|353|354|355|356|357|358|359|360|361|362|363|364|365|366|367|368|369|370|371|372|373|374|375|376|377|378|379|380|381|382|383|384|385|386|387|388|389|390|391|392|393|394|395|399|400|401|402|403|404|405|406|407|408|409|410|411|412|413|414|415|416|417|418|419|420|421|422|423|424|425|426|427|428|429|430|431|432|433|434|435|436|437|438|439|440|441|442|443|444|445|446|447|448|449|450|451|452|453|454|455|456|457|458|459|460|461|462|463|464|465|466|467|468|469|470|471|472|473|474|475|476|477|478|479|480|481|482|483|484|485|486|487|488|489|490|491|492|493|494|495|496|497|498|499|500|501|502|503|504|505|506|507|508|509|510|511|512|513|515|516|517|518|519|520|521|522|523|524|525|526|527|528|529|530|531|532|533|534|535|536|537|538|539|540|541|542|543|544|545|546|551|552|553|554|555|556|557|558|559|560|561|562|563|564|565|566|567|568|569|570|571|572|573|574|575|576|577|578|579|580|581|582|583|584|585|586|587|588|589|590|591|592|593|594|595|596|597|598|599|601|602|603|604|605|606|607|608|609|610|611|612|613|614|615|616|617|618|619|643|644|645|646|647|648|649|650|651|652|653|654|655|656|657|658|659|660|661|662|663|664|665|666|667|668|669|670|671|672|673|674|675|676|677|678|679|680|681|682|683|684|685|686|687|688|689|690|691|692|693|694|695|696|697|698|699|700|701|702|703|704|705|706|707|708|709|710|711|712|713|714|715|716|717|718|719|720|721|722|723|724|725|726|727|728|729|730|731|732|733|734|735|736|737|738|739|740|741|742|743|744|745|746|747|748|749|750|751|752|753|754|755|756|757|758|759|760|761|762|763|764|765|766|767|768|769|770|771|772|773|774|775|776|777|778|781|782|783|795|796|797|798|799|800|801|802|803|804|805|806|807|808|809|810|811|812|813|814|815|816|817|818|819|820|821|822|823|824|825|826|827|828|829|830|831|832|833|834|835|836|838|839|840|841|842|843|844|845|847|848|849|850|851|852|853|854|855|856|857|858|859|860|861|862|863|864|865|866|867|868|869|870|871|872|873|874|875|876|877|878|879|880|881|882|883|884|885|886|887|935|936|937|938|939|940|941|942|943|944|945|946|947|948|949|950|951|952|953|954|955|956|957|958|959|960|961|962|963|964|965|966|967|968|969|970|971|972|973|974|975|976|977|978|979|980|981|982|983|984|985|986|987|988|989|990|991|992|993|994|995|996|997|998|999|1000|1001|1002|1003|1004|1005|1006|1007|1008|1009|1010|1011|1012|1013|1014|1015|1016|1017|1018|1019|1020|1021|1022|1023|1029|1030|1036|1044|1045|1046|1047|1048|1049|1050|1051|1052|1053|1054|1055|1056|1057|1058|1059|1060|1061|1062|1063|1064|1065|1066|1067|1068|1069|1070|1071|1072|1073|1074|1075|1076|1077|1078|1079|1080|1081|1082|1083|1084|1085|1086|1087|1088|1089|1090|1091|1092|1093|1094|1095|1096|1097|1098|1099|1100|1101|1103|1104|1105|1106|1107|1108|1109|1110|1111|1112|1113|1114|1115|1116|1117|1118|1119|1120|1121|1122|1123|1124|1125|1126|1127|1128|1130|1131|1132|1133|1134|1135|1136|1137|1138|1139|1140|1141|1142|1143|1144|1145|1146|1147|1151|1152|1153|1154|1155|1156|1157|1158|1159|1160|1161|1162|1163|1164|1165|1166|1167|1168|1169|1170|1171|1172|1184|1185|1186|1187|1188|1189|1190|1191|1192|1193|1194|1195|1196|1197|1198|1199|1200|1201|1202|1203|1204|1205|1206|1207|1208|1209|1210|1211|1212|1213|1214|1215|1216|1217|1218|1219|1220|1221|1222|1223|1224|1225|1226|1227|1228|1229|1230|1231|1232|1233|1234|1235|1236|1237|1238|1239|1240|1241|1242|1243|1244|1245|1246|1247|1248|1249|1250|1251|1252|1253|1254|1260|1261|1262|1263|1264|1265|1266|1267|1268|1269|1270|1271|1272|1273|1274|1275|1276|1277|1278|1279|1280|1281|1282|1283|1284|1285|1286|1287|1288|1289|1290|1295|1296|1297|1298|1299|1300|1301|1302|1303|1380|1381|1382|1383|1384|1385|1386|1387|1388|1389|1390|1391|1392|1393|1394|1395|1396|1397|1398|1400|1401|1402|1403|1404|1405|1406|1407|1408|1419|1432|1433|1434|1435|1436|1437|1438|1439|1440|1441|1442|1443|1444|1445|1446|1447|1448|1449|1450|1451|1452|1453|1454|1455|1456|1457|1458|1459|1460|1461|1462|1463|1464|1465|1466|1467|1468|1469|1470|1471|1472|1473|1474|1475|1476|1477|1478|1479|1480|1481|1482|1483|1484|1485|1486|1487|1488|1489|1490|1491|1492|1493|1494|1495|1496|1497|1498|1500|1501|1520|1521|1522|1523|1555|1556|1557|1558|1559|1560|1561|1562|1563|1564|1565|1566|1567|1568|1569|1570|1571|1572|1573|1574|1575|1576|1577|1578|1579|1580|1581|1582|1583|1584|1585|1586|1587|1588|1589|1590|1591|1592|1593|1594|1595|1596|1597|1598|1599|1600|1601|1602|1603|1604|1605|1606|1607|1608|1609|1610|1611|1612|1613|1614|1615|1616|1617|1618|1619|1620|1621|1622|1623|1624|1625|1711|1712|1713|1714|1715|1841|1842|1855|1856|1857|1858|1859|1860|1870|1883|1904|1905|1906|1907|1908|1909|1910|1911|1912|1913|1914|1915|1916|1917|1918|1919|1920|1921|1922|1923|1924|1925|1926|1927|1928|1929|1930|1931|1932|1933|1934|1935|1936|1937|1938|1939|1940|1941|1942|1943|1948|1949|1950|1951|1952|1953|1954|1955|1956|1957|1958|1959|1960|1961|1962|1963|1964|1965|1966|1967|1968|1969|1970|1971|1972|1973|1974|1975|1976|1977|1978|1979|1980|1981|1982|1984|1985|1986|1987|1988|1994|1995|1996|1997|1998|1999|2000|2001|2003|2004|2005|2006|2007|2008|2009|2010|2011|2012|2013|2014|2015|2016|2017|2018|2019|2020|2021|2022|2023|2024|2025|2026|2027|2028|2029|2030|2031|2032|2033|2034|2035|2036|2037|2038|2039|2040|2041|2042|2043|2044|2045|2046|2047|2048|2049|2050|2051|2052|2053|2054|2055|2056|2058|2059|2060|2061|2062|2063|2064|2065|2066|2067|2068|2070|2071|2072|2073|2074|2075|2076|2077|2078|2079|2080|2081|2082|2121|2122|2123|2124|2125|2131|2132|2133|2134|2135|2136|2137|2138|2139|2140|2141|2142|2143|2144|2145|2146|2147|2148|2149|2150|2151|2152|2153|2154|2155|2156|2157|2158|2159|2160|2161|2162|2163|2164|2165|2193|2194|2195|2196|2197|2198|2199|2200|2201|2202|2210|2211|2212|2213|2214|2215|2216|2217|2218|2219|2220|2221|2222|2223|2225|2226|2227|2228|2229|2230|2231|2232|2233|2234|2235|2236|2237|2238|2239|2240|2241|2242|2243|2244|2245|2246|2247|2248|2249|2250|2251|2252|2253|2254|2255|2256|2257|2258|2259|2260|2261|2262|2263|2264|2265|2266|2267|2268|2269|2270|2271|2275|2276|2277|2278|2279|2280|2281|2282|2283|2284|2285|2286|2287|2288|2289|2290|2291|2292|2293|2294|2295|2296|2297|2298|2299|2300|2301|2302|2303|2304|2305|2306|2307|2308|2309|2310|2311|2312|2313|2314|2315|2316|2317|2318|2319|2320|2321|2323|2324|2325|2326|2327|2328|2329|2330|2331|2332|2333|2334|2335|2336|2337|2338|2339|2340|2341|2342|2343|2344|2345|2346|2347|2348|2349|2350|2351|2352|2353|2354|2355|2356|2357|2358|2359|2362|2363|2364|2365|2366|2367|2368|2369|2370|2371|2372|2373|2374|2375|2376|2377|2378|2379|2380|2381|2382|2383|2384|2385|2386|2387|2388|2389|2390|2391|2392|2393|2394|2395|2396|2397|2398|2399|2400|2401|2402|2403|2404|2405|2406|2407|2408|2409|2410|2411|2412|2413|2414|2415|2416|2417|2418|2419|2420|2421|2422|2458|2459|2460|2461|2462|2463|2464|2465|2466|2467|2468|2469|2470|2471|2472|2473|2474|2475|2476|2477|2478|2479|2480|2481|2482|2483|2484|2485|2486|2487|2488|2489|2490|2491|2492|2493|2494|2495|2496|2497|2498|2499|2500|2501|2502|2503|2504|2505|2506|2507|2508|2509|2510|2511|2512|2513|2514|2515|2516|2517|2518|2519|2520|2521|2522|2523|2524|2525|2526|2527|2528|2529|2530|2531|2532|2533|2534|2535|2536|2537|2539|2540|2541|2542|2543|2544|2545|2546|2547|2548|2549|2550|2551|2552|2553|2554|2555|2556|2557|2558|2559|2560|2561|2562|2563|2564|2565|2566|2567|2568|2569|2570|2571|2572|2573|2574|2575|2576|2577|2578|2579|2580|2581|2582|2583|2584|2585|2586|2587|2588|2589|2590|2591|2592|2593|2594|2595|2596|2597|2598|2599|2600|2601|2602|2603|2604|2605|2606|2607|2608|2609|2610|2611|2612|2613|2614|2615|2616|2617|2618|2619|2620|2621|2622|2623|2624|2625|2626|2627|2628|2629|2630|2631|2632|2633|2634|2635|2636|2637|2638|2639|2640|2641|2642|2643|2644|2645|2646|2647|2648|2649|2650|2651|2652|2653|2662|2663|2664|2665|2666|2667|2668|2669|2670|2671|2673|2682|2684|2685|2686|2687|2688|2689|2690|2691|2692|2694|2696|2697|2698|2699|2701|2702|2703|2704|2705|2706|2707|2708|2709|2710|2711|2712|2713|2714|2715|2716|2717|2718|2719|2720|2721|2722|2723|2724|2725|2726|2727|2728|2729|2730|2731|2732|2733|2734|2735|2736|2737|2738|2740|2741|2744|2745|2746|2747|2748|2749|2750|2751|2752|2753|2754|2755|2756|2757|2758|2759|2760|2761|2762|2763|2764|2765|2766|2767|2768|2769|2770|2771|2772|2773|2775|2776|2777|2778|2779|2780|2781|2782|2783|2784|2785|2786|2787|2788|2789|2790|2791|2792|2793|2794|2795|2796|2797|2798|2799|2800|2801|2803|2804|2805|2806|2807|2808|2809|2810|2811|2812|2813|2814|2815|2816|2817|2818|2819|2820|2821|2822|2823|2824|2825|2826|2827|2828|2829|2830|2831|2832|2833|2834|2835|2836|2837|2838|2839|2840|2841|2842|2843|2844|2845|2846|2847|2848|2849|2850|2851|2852|2853|2854|2855|2856|2857|2858|2859|2860|2861|2862|2863|2864|2865|2866|2867|2868|2869|2870|2871|2872|2873|2874|2875|2876|2877|2878|2879|2880|2881|2882|2883|2884|2885|2886|2887|2888|2889|2890|2891|2892|2893|2894|2895|2896|2897|2898|2899|2900|2901|2902|2903|2904|2905|2906|2907|2908|2910|2911|2912|2913|2914|2915|2916|2917|2918|2919|2920|2921|2923|2924|2925|2926|2927|2928|2929|2930|2931|2932|2933|2934|2936|2937|2938|2939|2940|2941|2942|2943|2944|2945|2946|2947|2948|2949|2950|2951|2952|2953|2954|2955|2956|2957|2958|2959|2960|2961|2962|2963|2964|2965|2966|2967|2968|2969|2970|2971|2972|2973|2974|2975|2976|2977|2978|2979|2980|2981|2982|2983|2984|2985|2986|2987|2988|2989|2990|2992|2993|2994|2995|3003|3004|3005|3006|3007|3008|3009|3010|3011|3012|3013|3014|3015|3016|3017|3018|3019|3020|3021|3022|3023|3024|3025|3026|3027|3028|3029|3030|3031|3032|3033|3034|3035|3036|3037|3038|3039|3040|3041|3042|3043|3044|3045|3046|3047|3048|3049|3050|3051|3052|3053|3054|3055|3056|3057|3058|3059|3060|3061|3062|3063|3064|3065|3066|3067|3068|3069|3070|3071|3072|3073|3074|3075|3076|3077|3079|3080|3081|3082|3083|3084|3085|3086|3087|3088|3089|3090|3091|3092|3093|3094|3095|3096|3097|3098|3099|3100|3101|3102|3103|3104|3105|3106|3107|3108|3109|3110|3111|3112|3113|3114|3115|3116|3117|3118|3119|3120|3121|3122|3123|3124|3125|3126|3127|3128|3129|3130|3131|3132|3133|3134|3135|3136|3137|3138|3139|3140|3141|3142|3143|3144|3145|3146|3147|3148|3149|3150|3151|3152|3153|3154|3316|3317|3318|3319|3320|3321|3322|3323|3324|3325|3326|3327|3328|3329|3330|3331|3367|3368|3369|3370|3371|3372|3373|3374|3375|3376|3377|3378|3379|3380|3381|3382|3383|3384|3385|3386|3387|3388|3389|3390|3391|3392|3393|3394|3395|3396|3397|3398|3399|3400|3401|3402|3403|3404|3405|3406|3407|3408|3409|3410|3411|3412|3413|3414|3415|3416|3417|3418|3419|3420|3421|3422|3423|3424|3425|3426|3427|3428|3429|3430|3431|3432|3433|3434|3436|3437|3438|3439|3440|3441|3447|3449|3451|3452|3453|3455|3460|3461|3462|3463|3464|3467|3468|3469|3470|3471|3472|3473|3474|3475|3476|3477|3478|3479|3480|3485|3500|3501|3502|3503|3505|3526|3527|3528|3529|3530|3531|3532|3533|3534|3535|3536|3537|3538|3539|3540|3541|3542|3543|3544|3545|3546|3547|3548|3549|3550|3551|3552|3553|3554|3555|3556|3557|3558|3559|3560|3561|3562|3563|3564|3565|3566|3567|3568|3569|3570|3571|3572|3573|3574|3575|3576|3577|3578|3579|3580|3581|3582|3583|3584|3590|3596|3597|3599|3600|3601|3602|3603|3604|3605|3606|3607|3608|3609|3610|3611|3612|3613|3614|3615|3616|3617|3618|3619|3620|3621|3622|3623|3624|3625|3626|3627|3628|3629|3630|3631|3632|3633|3634|3635|3636|3637|3638|3639|3640|3641|3642|3643|3644|3645|3646|3650|3651|3652|3653|3654|3655|3656|3657|3658|3659|3660|3661|3662|3663|3664|3670|3671|3672|3674|3675|3676|3785|3786|3787|3788|3789|3790|3791|3792|3793|3794|3795|3826|3827|3828|3829|3830|3831|3832|3833|3834|3835|3836|3837|3838|3839|3840|3841|3842|3843|3850|3860|3861|3862|3863|3864|3865|3866|3867"

function get_random_say()
	local random_say = {
		"今天天气真好，你不去打球却在玩mud...",
		"八毛，你exp多少了？",
		"et你是外星人么，每次叫你，你都不回答。在泡妞还是玩mud。",
		"凯歌job npc 好bt，能改的容易点么。死死不健康啊。",
		"破船，少林怎么这么差，你说的比唱的好听。",
		"无语了",
		"萌萌嗒！",
		"怎么说好呢",
		"其实也没啥",
		"谁让我长的帅呢",
		"秋姑早...",
		"我也是这么想的",
		"说实话，自动quest真没意思，还是手动好",
		"啥时候开武林大会就好了",
		"ok",
		"不行，绝对不",
		"老是这样，烦死人了",
		"BE呼叫BE",
		"QQ群号啥的忘了， 77634998么，谁知道",
		"不",
		"搞毛，总是死",
		"也就这样了",
		"到底zmud好呢还是mc好？秋猫你说",
		"谁的机器人总是杀贾布...",
		"你们hs1别get corpse好吧",
		"啥时候增加新quest",
		"没",
		"毛豆不剩，sorry，毛都不剩"
	}
	local random_say2 = {
		"(-__-)b",
		"('?')",
		"，。。。当我没说",
		"。真是..",
		"，快说啊",
		"。算了",
	}
	local a = math.random(#random_say)
	local b = math.random(#random_say2)
	local say = random_say[a] .. random_say2[b]
	return say
end

function check_room_obj(_name) --检查房间是否存在_name
	local _name = _name or "_none"
	local _obj = var["roomobj"] or {}
	local _exist = false
	for k, v in pairs(_obj) do
		if (string.find(k, " " .. _name .. "$") or string.find(k, "」" .. _name .. "$") or string.find(k, "位" .. _name .. "$") or string.find(k, "^" .. _name .. "$")) and v ~= "corpse" then
			_exist = true
			break
		end
	end
	return _exist
end

function check_room_obj_id(_name) --检查房间是否存在id 比如du she
	local _name = _name or "_none"
	local _obj = var["roomobj"] or {}
	local _exist = false
	for k, v in pairs(_obj) do
		if v == _name then
			_exist = true
			break
		end
	end
	return _exist
end

function get_room_obj_id(_name) --检查房间是否存在_name,给出id
	local _name = _name or "_none"
	local _obj = var["roomobj"] or {}
	local _exist = ""
	for k, v in pairs(_obj) do
		if string.find(k, _name .. "$") and v ~= "corpse" then
			_exist = v
			break
		end
	end
	return _exist
end

npc_in_maze_found = function()
	return nil
end         --迷宫发现npc
npc_in_maze_action = function()
	return nil --迷宫发现npc的行为
end

function get_first_pfm(s)
	if s == nil then
		return "kick"
	elseif s == "" then
		return "kick"
	else
		return s
	end
end

------得到任务房间列表
function get_job_rooms(zone_room, range, killer_name, killer_party, killer_skill, black_list, fangqi_party, fangqi_skill,
					   fangqi_zone_room, fangqi_zone, sort, central)                                                                                         --得到job房间的table
	--return room_list,search_list,search_list2,job_zone,job_room,job_range 返回这些变量
	--central 只搜中原
	local zone_room = zone_room or "不存在"
	local range = range or 1 --范围
	local fangqi_party = fangqi_party or "none"
	local fangqi_skill = fangqi_skill or "none"
	local fangqi_zone_room = fangqi_zone_room or "none"
	local fangqi_zone = fangqi_zone or "none"
	local black_list = black_list or "none" --黑名单
	local killer_name = killer_name or "false"
	--	local killer_party=var["killer_party"] or "false"
	--	local killer_skill=var["killer_skill"] or "false"
	local killer_party = killer_party or "false"
	local killer_skill = killer_skill or "false"
	local myexp = var["exp"] or 150000 --id exp
	local myshen = var["shen"] or 0 --id shen
	local myparty = var["party"] or "false"
	local optimize = 0            --未优化
	local range_2 = range
	local central_area =
	"|扬州城|长乐帮|黄河流域|泰山|南阳城|襄阳城|嵩山少林|嵩山|长安城|华山|苗疆|铁掌山|成都城|武当山|峨嵋山|大理城|大理城东|大理城南|玉虚观|大理王府|大理皇宫|天龙寺|大理城西|无量山|大雪山|柳宗镇|终南山|蝴蝶谷|桃源县|萧府|华山村|武当后山|襄阳郊外|古墓派|全真教|蒙古|血刀门|"
	if fangqi_party == "" then fangqi_party = "none" end
	if fangqi_skill == "" then fangqi_skill = "none" end
	if fangqi_zone_room == "" then fangqi_zone_room = "none" end
	if fangqi_zone == "" then fangqi_zone = "none" end

	if string.find("|" .. fangqi_zone_room .. "|", "|" .. zone_room .. "|") then --放弃这个房间，如扬州城北大街
		var["log_zone"], var["log_room"] = break_zone_room(zone_room)
		--		echo("\n"..C.m.."job.lua->get_job_rooms:return "..1)
		return nil, nil, nil, nil, nil, nil --return room_list,search_list,job_zone,job_room 返回这些变量
	elseif central ~= nil and string.find("扬州城江南官道|扬州城长江南岸|黄河流域大渡口|长安城大道|长安城陕晋渡口|大理城南澜沧江边|大理城南林间道", zone_room) then
		return nil, nil, nil, nil, nil, nil
	elseif string.find("|" .. black_list .. "|", "|" .. killer_name .. "|") or string.find("|" .. fangqi_party .. "|", "|" .. killer_party .. "|") or string.find("|" .. fangqi_skill .. "|", "|" .. killer_skill .. "|") then --人物在黑名单中
		--		echo("\n"..C.m.."job.lua->get_job_rooms:return "..2)
		return nil, nil, nil, nil, nil, nil
	else
		local job_zone, job_room = break_zone_room(zone_room) --分解扬州城北大街

		if (zone_room == "扬州城瘦西湖酒馆" or zone_room == "扬州城瘦西湖雅楼" or zone_room == "归云庄小酒馆" or zone_room == "扬州城珠宝店" or zone_room == "华山思过崖洞口") and range == 0 then --这些地方 npc容易出去所有范围最好大一点
			range = range + 1
		elseif (zone_room == "扬州城赌场" or zone_room == "华山后堂" or zone_room == "华山正气堂" or zone_room == "苗疆药王居" or zone_room == "苗疆药房") and range < 2 then --nofight 地方太多范围大一点
			range = range + 2
		end

		var["log_zone"] = job_zone --log的变量
		var["log_room"] = job_room --log的变量
		if job_zone == "" and job_room == "" then --没找到？
			--			echo("\n"..C.m.."job.lua->get_job_rooms:return "..3)
			return nil, nil, nil, nil, nil, nil --返回数据
		elseif job_zone == "天山" and (var["close_tianshan"] == nil or var["close_tianshan"] ~= 0) and myshen > 0 then --正神不做ts
			--			echo("\n"..C.m.."job.lua->get_job_rooms:return "..4)
			return nil, nil, nil, nil, nil, nil
		elseif string.find("|" .. fangqi_zone .. "|", "|" .. job_zone .. "|") then --放弃这个区域
			--			echo("\n"..C.m.."job.lua->get_job_rooms:return "..5)
			return nil, nil, nil, nil, nil, nil
		elseif central ~= nil and not string.find(central_area, "|" .. job_zone .. "|") then
			return nil, nil, nil, nil, nil, nil
		else
			local _, room_list, room_list_check = get_room_list(job_zone, job_room, lua_flags)
			if null(room_list) == true then --没找到房间列表
				--			echo("\n"..C.m.."job.lua->get_job_rooms:return "..6)
				return nil, nil, nil, nil, nil, nil
			elseif room_list[1] ~= 51 and pathfrom(51, room_list[1], lua_flags) == "" then --找到房间列表但是从房间51 无法到达
				--				echo("\n"..C.m.."job.lua->get_job_rooms:return "..7)
				return nil, nil, nil, nil, nil, nil
			elseif myexp < 800000 and not string.find("|" .. var["newbie_rooms"] .. "|", "|" .. room_list[1] .. "|") then --exp<800k 的时候房间不在保护区域里面
				--				echo("\n"..C.m.."job.lua->get_job_rooms:return "..8)
				return nil, nil, nil, nil, nil, nil
			else
				local _, search_list = get_searches(room_list, range, lua_flags, 1) --得到范围range的所有房间

				--------------------------------------------------------开始过滤特殊房间-----------------------------------------------------------
				local remove_item, zone_only, search_list_new, search_list2_new, newbie = {}, {}, {}, {}, 0

				if room_list_check[2118] then --明教几个门一次只搜一个
					remove_item[2119] = true
					remove_item[2120] = true
					remove_item[2126] = true
				elseif room_list_check[2119] then --明教几个门一次只搜一个
					remove_item[2118] = true
					remove_item[2120] = true
					remove_item[2126] = true
				elseif room_list_check[2120] then --明教几个门一次只搜一个
					remove_item[2119] = true
					remove_item[2118] = true
					remove_item[2126] = true
				elseif room_list_check[2126] then --明教几个门一次只搜一个
					remove_item[2119] = true
					remove_item[2120] = true
					remove_item[2118] = true
				elseif string.find(job_zone, "明教") then --明教几个门一次只搜一个
					remove_item[2119] = true
					remove_item[2120] = true
					remove_item[2126] = true
				end

				if var["usefire"] and var["usefire"] == 1 then
					remove_item[1077] = true --删除九老洞
				end

				if ((myparty == "姑苏慕容" or myparty == "普通百姓") and string.find("|神龙岛|逍遥派|桃花岛|", "|" .. job_zone .. "|")) or ((myparty ~= "姑苏慕容" and myparty ~= "普通百姓") and string.find("|曼佗罗山庄|姑苏慕容|燕子坞|神龙岛|逍遥派|桃花岛|", "|" .. job_zone .. "|")) then --岛屿地区只搜索本区域
					for k, v in pairs(rooms) do
						if v["area"] == job_zone then
							zone_only[k] = true
						end
					end
				end

				if myexp < 800000 then newbie = 1 end --exp<800k为新手保护区域

				if null(zone_only) == true then --独立区域没排除房间
					for k, v in pairs(search_list) do
						if not remove_item[v] then
							if newbie == 1 and not string.find(var["newbie_rooms"], "|" .. v .. "|") then
								if myexp > 800000 then                                                --经验高								
									--central 只搜中原
									if central == nil or (central ~= nil and string.find(central_area, "|" .. rooms[v].area .. "|")) then --注意这样可以剔除非中原房间，map数据有序就是方便撒
										table.insert(search_list_new, v)
										table.insert(search_list2_new, v)
									end
								end
							else
								if central == nil or (central ~= nil and string.find(central_area, "|" .. rooms[v].area .. "|")) then
									table.insert(search_list_new, v)
									table.insert(search_list2_new, v)
								end
							end
						end
					end
				else
					for k, v in pairs(search_list) do
						if not remove_item[v] and zone_only[v] then
							if not string.find(var["newbie_rooms"], "|" .. v .. "|") then
								if newbie == 1 and myexp > 800000 then
									if central == nil or (central ~= nil and string.find(central_area, "|" .. rooms[v].area .. "|")) then
										table.insert(search_list_new, v)
										table.insert(search_list2_new, v)
									end
								end
							else
								if central == nil or (central ~= nil and string.find(central_area, "|" .. rooms[v].area .. "|")) then
									table.insert(search_list_new, v)
									table.insert(search_list2_new, v)
								end
							end
						end
					end
				end
				if sort and sort == 1 then --sort 1
					table.sort(search_list_new) --房间从新排列，按照最佳路径，如果设置sort 1
					table.sort(search_list2_new)
					optimize = 1
				end
				if string.find("|嵩山少林达摩洞|嵩山少林竹林|嵩山少林山路|嵩山少林小路|嵩山少林石板路|嵩山少林练武场|嵩山少林石阶|嵩山少林塔林|长安城民居|长安城?t望塔|长安城箭楼|长安城官道|长安城东城墙|长安城西城墙|长安城南城墙|长安城北城墙|苗疆山路|铁掌山山路|铁掌山荒草路|武当山古神道|武当山石阶|武当山后山小院|武当山院门|武当山小径|峨嵋山后山小路|峨嵋山九十九道湾|峨嵋山禅房|峨嵋山古德林|峨嵋山冷杉林|大理城东大街|大理城西大街|大理城南大街|大理城北大街|天龙寺松树林|天龙寺龙树院|华山松树林|华山树林|无量山大松林|无量山山路|无量山荆棘林|大雪山遮雨廊|大雪山牧场|终南山黑林|桃源县山洞|桃源县岸边|桃源县山顶|桃源县山坡|桃源县石梁|桃源县石梁尽头|桃源县荷塘|桃源县小石桥|桃源县东厢房|桃源县西厢房|桃源县大殿|桃源县后院|桃源县练功房|桃源县厨房|桃源县青石小径|桃源县竹林|桃源县石屋正房|桃源县石屋厢房|兰州城大道|星宿海星宿海|星宿海大沙漠|星宿海南疆沙漠|回疆针叶林|回疆大草原|回疆草原边缘|大草原草海|大草原沼泽|明教树林|明教长廊|明教练武场|明教紫衫林||明教小沙丘|明教天字门|明教地字门|明教风字门|明教雷字门|昆仑山云杉林|平定州土路|神龙岛树林|神龙岛山路|神龙岛卧室|神龙岛练功房|杭州城青石大道|杭州城西湖边|杭州城长廊|福州城山路|福州城渔船|佛山镇小路|佛山镇林间道|莆田少林山路|莆田少林小路|莆田少林侧廊|曼佗罗山庄长廊|曼佗罗山庄小径|燕子坞长廊|丐帮杏子林|武当后山烈火丛林|武当后山积雪丛林|武当后山落叶丛林|武当后山阔叶丛林|武当后山丛林边缘|襄阳郊外树林|全真教石阶|蒙古大沙漠|兰州城大沙漠|青城大沙漠|", "|" .. zone_room .. "|") then
					table.sort(search_list_new) --房间从新排列，按照最佳路径,对于一些迷宫和房间比较多的分散的房间，按照最佳路径重排
					--强制性重排
					table.sort(search_list2_new)
					optimize = 1
				end

				if string.find("|回疆|星宿海|大草原|桃源县|", "|" .. job_zone .. "|") then
					table.sort(search_list_new) --强制性重排
					table.sort(search_list2_new)
					optimize = 1
				end

				room_list_check = nil
				search_list = nil
				remove_item = nil
				zone_only = nil

				------------------------------------------------------------------特殊房间过滤结束-----------------------------------------------
				--				return nil,nil,nil,nil,nil,nil --return room_list,search_list,search_list2,job_zone,job_room,job_range 返回这些变量
				zone_room = nil
				fangqi_party = nil
				fangqi_skill = nil
				fangqi_zone_room = nil
				fangqi_zone = nil
				killer_name = nil
				killer_party = nil
				killer_skill = nil
				black_list = nil
				myexp = nil
				myshen = nil
				myparty = nil
				table.sort(room_list)
				if table.maxn(room_list) == 1 and optimize == 1 and range_2 ~= 0 then --单独目标房间情况下，在searchlist中将该房间排在第一个	
					table.insert(search_list_new, 1, room_list[1])
					table.insert(search_list2_new, 1, room_list[1])
				end
				optimize = nil
				range_2 = nil
				if null(search_list_new) then
					--				echo("\n"..C.m.."job.lua->get_job_rooms:return "..9)
					return nil, nil, nil, nil, nil, nil
				else
					--				echo("\n"..C.m.."job.lua->get_job_rooms:return "..10)
					return room_list, search_list_new, search_list2_new, job_zone, job_room, range --yes,得到信息了
				end
			end
		end
	end
end

--新加入job流程控制
--chuchen zi
--yang xiao
--xingxiu dizi
--huizhen zunzhe
--jiao zhong
--shi zhe
--zhiqin bing
--ju mang
--ye zhu
--bai xiong
--bao zi
--lao hu
--snake
--du she
--ma zei
--zhuye qing
--huilang
--toulang
--pingkou jiangjun
--cheqi jiangjun
--zhechong jiangjun
--zhengdong jiangjun
--  三只黑色毒蛇(Heise dushe)

Print("--- 加载模块: 任务 ---")
